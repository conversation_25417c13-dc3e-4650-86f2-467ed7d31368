# Copyright Broadcom, Inc. All Rights Reserved.
# SPDX-License-Identifier: APACHE-2.0

## @section Global parameters
## Global Docker image parameters
## Please, note that this will override the image parameters, including dependencies, configured to use the global value
## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass
##

## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker registry secret names as an array
## @param global.defaultStorageClass Global default StorageClass for Persistent Volume(s)
## @param global.storageClass DEPRECATED: use global.defaultStorageClass instead
##
global:
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  defaultStorageClass: ""
  storageClass: parse-server
  ## Compatibility adaptations for Kubernetes platforms
  ##
  compatibility:
    ## Compatibility adaptations for Openshift
    ##
    openshift:
      ## @param global.compatibility.openshift.adaptSecurityContext Adapt the securityContext sections of the deployment to make them compatible with Openshift restricted-v2 SCC: remove runAsUser, runAsGroup and fsGroup and let the platform use their allowed default IDs. Possible values: auto (apply if the detected running cluster is Openshift), force (perform the adaptation always), disabled (do not perform adaptation)
      ##
      adaptSecurityContext: auto
## @section Common Parameters
##

## @param nameOverride String to partially override common.names.fullname template (will maintain the release name)
##
nameOverride: ""
## @param fullnameOverride String to fully override common.names.fullname template
##
fullnameOverride: ""
## @param namespaceOverride String to fully override common.names.namespace
##
namespaceOverride: ""
## @param kubeVersion Force target Kubernetes version (using Helm capabilites if not set)
##
kubeVersion: ""
## @param commonLabels Add labels to all the deployed resources
##
commonLabels: {}
## @param commonAnnotations Add annotations to all the deployed resources
##
commonAnnotations: {}
## @param extraDeploy Array of extra objects to deploy with the release
##
extraDeploy: []
## Enable diagnostic mode in the deployment
##
diagnosticMode:
  ## @param diagnosticMode.enabled Enable diagnostic mode (all probes will be disabled and the command will be overridden)
  ##
  enabled: false
  ## @param diagnosticMode.command Command to override all containers in the deployment
  ##
  command:
    - sleep
  ## @param diagnosticMode.args Args to override all containers in the deployment
  ##
  args:
    - infinity
## @section Parse server parameters
##

## loadBalancerIP for the Parse Service (optional, cloud specific)
## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
##
server:
  ## Bitnami Parse image version
  ## ref: https://hub.docker.com/r/bitnami/parse/tags/
  ## @param server.image.registry [default: REGISTRY_NAME] Parse image registry
  ## @param server.image.repository [default: REPOSITORY_NAME/parse] Parse image repository
  ## @skip server.image.tag Parse image tag (immutable tags are recommended)
  ## @param server.image.digest Parse image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param server.image.pullPolicy Image pull policy
  ## @param server.image.pullSecrets Specify docker-registry secret names as an array
  ## @param server.image.debug Enable image debug mode
  ##
  image:
    registry: docker.io
    repository: bitnami/parse
    tag: 7.2.0-debian-12-r2
    digest: ""
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
    ## Enable debug mode
    ##
    debug: false
  ## @param server.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param server.hostAliases Deployment pod host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param server.podLabels Extra labels for Parse pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param server.podAnnotations Annotations for Parse pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param server.forceOverwriteConfFile Overwrite config.json configuration file on each run (set to false if mounting a custom configuration file)
  ##
  forceOverwriteConfFile: true
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param server.podSecurityContext.enabled Enabled Parse Dashboard pods' Security Context
  ## @param server.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param server.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param server.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param server.podSecurityContext.fsGroup Set Parse Dashboard pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
  ## @param server.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param server.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param server.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param server.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param server.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param server.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param server.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param server.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param server.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param server.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param server.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param server.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param server.containerPorts.http Parse server port
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse#configuration
  ##
  containerPorts:
    http: 1337
  ## @param server.mountPath Parse server API mount path
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse#configuration
  ##
  mountPath: /parse
  ## @param server.appId Parse server App ID
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse#configuration
  ##
  appId: myappID
  ## @param server.masterKey Parse server Master Key
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse#configuration
  ##
  masterKey: ""
  ## @param server.masterKeyIps Parse server Master setting an allowed IP address
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse#configuration
  ##
  masterKeyIps: "0.0.0.0/0"
  ## @param server.extraEnvVars An array to add extra env vars
  ## For example:
  ## extraEnvVars:
  ##  - name: PARSE_SERVER_ALLOW_CLIENT_CLASS_CREATION
  ##    value: "true"
  ##
  extraEnvVars: []
  ## @param server.extraEnvVarsCM Name of a ConfigMap containing extra environment variables
  ##
  extraEnvVarsCM: ""
  ## @param server.extraEnvVarsSecret Name of a Secret containing extra environment variables
  ##
  extraEnvVarsSecret: ""
  ## @param server.extraVolumes Optionally specify extra list of additional volumes for the Parse pod(s)
  ##
  extraVolumes: []
  ## @param server.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Parse container(s)
  ##
  extraVolumeMounts: []
  ## @param server.sidecars Add additional sidecar containers to the Parse pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param server.initContainers Add additional init containers to the Parse pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## Pod Disruption Budget configuration
  ## ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb
  ## @param server.pdb.create Enable/disable a Pod Disruption Budget creation
  ## @param server.pdb.minAvailable Minimum number/percentage of pods that should remain scheduled
  ## @param server.pdb.maxUnavailable Maximum number/percentage of pods that may be made unavailable. Defaults to `1` if both `server.pdb.minAvailable` and `server.pdb.maxUnavailable` are empty.
  ##
  pdb:
    create: true
    minAvailable: ""
    maxUnavailable: ""
  ## @param server.enableCloudCode Enable Parse Cloud Code
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse#how-to-deploy-your-cloud-functions-with-parse-cloud-code
  ##
  enableCloudCode: true
  ## @param server.cloudCodeScripts Cloud Code scripts
  ## Specify dictionary of Cloud Code scripts and content
  ## Alternatively, you can put your scripts under the files/cloud directory
  ##
  ## cloudCodeScripts:
  ##   main.js: |
  ##      Parse.Cloud.define("sayHelloWorld", function(request, response) {
  ##        return "Hello world!";
  ##      });
  ##
  cloudCodeScripts: {}
  ## @param server.existingCloudCodeScriptsCM ConfigMap with Cloud Code scripts (Note: Overrides `cloudCodeScripts`).
  ## NOTE: This will override cloudCodeScripts
  ##
  existingCloudCodeScriptsCM: ""
  ## @param server.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if server.resources is set (server.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param server.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ##
  resources: {}
  ## Parse Server pods' liveness probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes)
  ## @param server.livenessProbe.enabled Enable livenessProbe
  ## @param server.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param server.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param server.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param server.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param server.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    initialDelaySeconds: 120
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## Parse Server pods' readiness probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes)
  ## @param server.readinessProbe.enabled Enable readinessProbe
  ## @param server.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param server.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param server.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param server.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param server.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param server.startupProbe.enabled Enable startupProbe on Parse containers
  ## @param server.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param server.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param server.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param server.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param server.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 0
    periodSeconds: 3
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 15
  ## @param server.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param server.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param server.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param server.podAffinityPreset Parse server pod affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param server.podAntiAffinityPreset Parse server pod anti-affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param server.nodeAffinityPreset.type Parse server node affinity preset type. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param server.nodeAffinityPreset.key Parse server node label key to match Ignored if `affinity` is set.
    ## E.g.
    ## key: "kubernetes.io/e2e-az-name"
    ##
    key: ""
    ## @param server.nodeAffinityPreset.values Parse server node label values to match. Ignored if `affinity` is set.
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param server.affinity Parse server affinity for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## Note: server.podAffinityPreset, server.podAntiAffinityPreset, and server.nodeAffinityPreset will be ignored when it's set
  ##
  affinity: {}
  ## @param server.nodeSelector Parse server node labels for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector:
    worker_group: services
  ## @param server.tolerations Parse server tolerations for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param server.updateStrategy.type Parse statefulset strategy type
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    ## StrategyType
    ## Can be set to RollingUpdate or OnDelete
    ##
    type: RollingUpdate
  ## @param server.priorityClassName Parse pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param server.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param server.schedulerName Name of the k8s scheduler (other than default) for Parse pods
  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param server.terminationGracePeriodSeconds Seconds Redmine pod needs to terminate gracefully
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods
  ##
  terminationGracePeriodSeconds: ""
  ## @param server.lifecycleHooks for the Parse container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## Kubernetes service for Parse Server Deployment
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services---service-types
  ##
  service:
    ## @param server.service.type Kubernetes Service type
    ##
    type: NodePort
    ## @param server.service.ports.http Service HTTP port (Dashboard)
    ##
    ports:
      http: 1337
    ## @param server.service.nodePorts.http Kubernetes HTTP node port
    ## nodePorts:
    ##   http: <to set explicitly, choose port between 30000-32767>
    ##
    nodePorts:
      http: ""
    ## @param server.service.clusterIP Service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param server.service.loadBalancerIP Service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param server.service.loadBalancerSourceRanges Service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param server.service.externalTrafficPolicy Service external traffic policy
    ## ref http://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param server.service.annotations Additional custom annotations for Service
    ##
    annotations: {}
    ## @param server.service.extraPorts Extra ports to expose in Service (normally used with the `sidecars` value)
    ##
    extraPorts: []
    ## @param server.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param server.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param server.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param server.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param server.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param server.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param server.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param server.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param server.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Dashboard Parameters
##
dashboard:
  ## @param dashboard.enabled Enable parse dashboard
  ##
  enabled: true
  ## Bitnami Parse Dashboard image version
  ## ref: https://hub.docker.com/r/bitnami/parse-dashboard/tags/
  ## @param dashboard.image.registry [default: REGISTRY_NAME] Dashboard image registry
  ## @param dashboard.image.repository [default: REPOSITORY_NAME/parse-dashboard] Dashboard image repository
  ## @skip dashboard.image.tag Dashboard image tag (immutable tags are recommended)
  ## @param dashboard.image.digest Dashboard image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param dashboard.image.pullPolicy image pull policy
  ## @param dashboard.image.pullSecrets Specify docker-registry secret names as an array
  ## @param dashboard.image.debug Enable Parse Dashboard image debug mode
  ##
  image:
    registry: docker.io
    repository: bitnami/parse-dashboard
    tag: 5.4.0-debian-12-r4
    digest: ""
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
    ## Enable debug mode
    ##
    debug: false
  ## @param dashboard.replicaCount Number of Parse Dashboard replicas to deploy
  ##
  replicaCount: 1
  ## @param dashboard.containerPorts.http Parse Dashboard HTTP container port
  ##
  containerPorts:
    http: 4040
  ## @param dashboard.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param dashboard.hostAliases Deployment pod host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param dashboard.podSecurityContext.enabled Enabled Parse Dashboard pods' Security Context
  ## @param dashboard.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param dashboard.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param dashboard.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param dashboard.podSecurityContext.fsGroup Set Parse Dashboard pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
  ## @param dashboard.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param dashboard.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param dashboard.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param dashboard.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param dashboard.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param dashboard.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param dashboard.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param dashboard.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param dashboard.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param dashboard.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param dashboard.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param dashboard.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param dashboard.username Parse Dashboard application username
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse-dashboard#configuration
  ##
  username: user
  ## @param dashboard.password Parse Dashboard application password
  ## Defaults to a random 10-character alphanumeric string if not set
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse-dashboard#configuration
  ##
  password: ""
  ## @param dashboard.appName Parse Dashboard application name
  ## ref: https://github.com/bitnami/containers/tree/main/bitnami/parse-dashboard#configuration
  ##
  appName: MyDashboard
  ## @param dashboard.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if dashboard.resources is set (dashboard.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param dashboard.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ##
  resources: {}
  ## Parse Dashboard pods' liveness probe
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes)
  ## @param dashboard.livenessProbe.enabled Enable livenessProbe
  ## @param dashboard.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param dashboard.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param dashboard.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param dashboard.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param dashboard.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    initialDelaySeconds: 240
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## Parse Dashboard pods' liveness probe
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes)
  ## @param dashboard.readinessProbe.enabled Enable readinessProbe
  ## @param dashboard.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param dashboard.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param dashboard.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param dashboard.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param dashboard.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param dashboard.startupProbe.enabled Enable startupProbe on Parse Dashboard containers
  ## @param dashboard.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param dashboard.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param dashboard.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param dashboard.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param dashboard.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 0
    periodSeconds: 3
    timeoutSeconds: 2
    failureThreshold: 15
    successThreshold: 1
  ## @param dashboard.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param dashboard.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param dashboard.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param dashboard.podLabels Extra labels for Parse Dashboard pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param dashboard.podAnnotations Annotations for Parse Dashboard pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param dashboard.podAffinityPreset Parse dashboard pod affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param dashboard.podAntiAffinityPreset Parse dashboard pod anti-affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ## Allowed values: soft, hard
  ##
  nodeAffinityPreset:
    ## @param dashboard.nodeAffinityPreset.type Parse dashboard node affinity preset type. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param dashboard.nodeAffinityPreset.key Parse dashboard node label key to match Ignored if `affinity` is set.
    ## E.g.
    ## key: "kubernetes.io/e2e-az-name"
    ##
    key: ""
    ## @param dashboard.nodeAffinityPreset.values Parse dashboard node label values to match. Ignored if `affinity` is set.
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param dashboard.affinity Parse dashboard affinity for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## Note: dashboard.podAffinityPreset, dashboard.podAntiAffinityPreset, and dashboard.nodeAffinityPreset will be ignored when it's set
  ##
  affinity: {}
  ## @param dashboard.nodeSelector Parse dashboard node labels for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param dashboard.tolerations Parse dashboard tolerations for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param dashboard.updateStrategy.type Parse statefulset strategy type
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    ## StrategyType
    ## Can be set to RollingUpdate or OnDelete
    ##
    type: RollingUpdate
  ## @param dashboard.priorityClassName Parse pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param dashboard.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param dashboard.schedulerName Name of the k8s scheduler (other than default) for Parse pods
  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param dashboard.terminationGracePeriodSeconds Seconds Redmine pod needs to terminate gracefully
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods
  ##
  terminationGracePeriodSeconds: ""
  ## @param dashboard.lifecycleHooks for the Parse container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param dashboard.parseServerUrlProtocol Protocol used by Parse Dashboard to form the URLs to Parse server
  ##
  parseServerUrlProtocol: "http"
  ## @param dashboard.extraEnvVars An array to add extra env vars
  ## For example:
  ## extraEnvVars:
  ##  - name: KIBANA_ELASTICSEARCH_URL
  ##    value: test
  ##
  extraEnvVars: []
  ## @param dashboard.extraEnvVarsCM Name of a ConfigMap containing extra environment variables
  ##
  extraEnvVarsCM: ""
  ## @param dashboard.extraEnvVarsSecret Name of a Secret containing extra environment variables
  ##
  extraEnvVarsSecret: ""
  ## @param dashboard.extraVolumes Optionally specify extra list of additional volumes for the Parse pod(s)
  ##
  extraVolumes: []
  ## @param dashboard.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Parse container(s)
  ##
  extraVolumeMounts: []
  ## @param dashboard.sidecars Add additional sidecar containers to the Parse pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param dashboard.initContainers Add additional init containers to the Parse pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## Pod Disruption Budget configuration
  ## ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb
  ## @param dashboard.pdb.create Enable/disable a Pod Disruption Budget creation
  ## @param dashboard.pdb.minAvailable Minimum number/percentage of pods that should remain scheduled
  ## @param dashboard.pdb.maxUnavailable Maximum number/percentage of pods that may be made unavailable. Defaults to `1` if both `dashboard.pdb.minAvailable` and `dashboard.pdb.maxUnavailable` are empty.
  ##
  pdb:
    create: true
    minAvailable: ""
    maxUnavailable: ""
  ## @param dashboard.forceOverwriteConfFile Overwrite config.json configuration file on each run (set to false if mounting a custom configuration file)
  ##
  forceOverwriteConfFile: true
  ## Kubernetes service for Parse Server Deployment
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services---service-types
  ##
  service:
    ## @param dashboard.service.type Kubernetes Service type
    ##
    type: NodePort
    ## @param dashboard.service.ports.http Service HTTP port (Dashboard)
    ##
    ports:
      http: 80
    ## @param dashboard.service.nodePorts.http Kubernetes HTTP node port
    ## nodePorts:
    ##   http: <to set explicitly, choose port between 30000-32767>
    ##
    nodePorts:
      http: ""
    ## @param dashboard.service.clusterIP Service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param dashboard.service.loadBalancerIP Service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param dashboard.service.loadBalancerSourceRanges Service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param dashboard.service.externalTrafficPolicy Service external traffic policy
    ## ref http://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param dashboard.service.annotations Additional custom annotations for Service
    ##
    annotations: {}
    ## @param dashboard.service.extraPorts Extra ports to expose in Service (normally used with the `sidecars` value)
    ##
    extraPorts: []
    ## @param dashboard.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param dashboard.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param dashboard.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param dashboard.networkPolicy.allowExternal Don't require label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## dashboard label will have network access to the ports dashboard is listening
    ## on. When true, dashboard will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param dashboard.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param dashboard.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param dashboard.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param dashboard.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param dashboard.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Traffic Exposure Parameters
##

## Configure the ingress resource that allows you to access the Parse installation.
## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/
##
ingress:
  ## @param ingress.enabled Set to true to enable ingress record generation
  ##
  enabled: true
  ## @param ingress.ingressClassName IngressClass that will be be used to implement the Ingress (Kubernetes 1.18+)
  ## This is supported in Kubernetes 1.18+ and required if you have more than one IngressClass marked as the default for your cluster .
  ## ref: https://kubernetes.io/blog/2020/04/02/improvements-to-the-ingress-api-in-kubernetes-1.18/
  ##
  ingressClassName: ""
  ## @param ingress.tls Enable TLS configuration for the hostname defined at ingress.hostname parameter
  ## TLS certificates will be retrieved from a TLS secret with name: {{- printf "%s-tls" .Values.ingress.hostname }}
  ## You can use the ingress.secrets parameter to create this TLS secret or relay on cert-manager to create it
  ##
  tls: false
  ## @param ingress.selfSigned Create a TLS secret for this ingress record using self-signed certificates generated by Helm
  ##
  selfSigned: false
  ## @param ingress.annotations Additional annotations for the Ingress resource. To enable certificate autogeneration, place here your cert-manager annotations.
  ## For a full list of possible ingress annotations, please see
  ## ref: https://github.com/kubernetes/ingress-nginx/blob/main/docs/user-guide/nginx-configuration/annotations.md
  ## Use this parameter to set the required annotations for cert-manager, see
  ## ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
  ##
  ## e.g:
  ## annotations:
  ##   kubernetes.io/ingress.class: nginx
  ##   cert-manager.io/cluster-issuer: cluster-issuer-name
  ##
  hostdomain: local
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: services-ingress
    alb.ingress.kubernetes.io/healthcheck-path: /health_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: 'worker_group=services'
  dashboard:
    ## @param ingress.dashboard.hostname Default host for the ingress resource
    ##
    hostname: parse-dashboard
    ## @param ingress.dashboard.path The Path to Parse. You may need to set this to '/*' in order to use this with ALB ingress controllers.
    ##
    path: /
    ## @param ingress.dashboard.pathType Ingress path type
    ##
    pathType: Prefix
    ## @param ingress.dashboard.extraHosts The list of additional hostnames to be covered with this ingress record.
    ## Most likely the hostname above will be enough, but in the event more hosts are needed, this is an array
    ## extraHosts:
    ## - name: parse.local
    ##   path: /
    ##
    extraHosts: []
    ## @param ingress.dashboard.extraPaths Any additional arbitrary paths that may need to be added to the ingress under the main host.
    ## For example: The ALB ingress controller requires a special rule for handling SSL redirection.
    ## extraPaths:
    ## - path: /*
    ##   backend:
    ##     serviceName: ssl-redirect
    ##     servicePort: use-annotation
    ##
    extraPaths: []
  server:
    ## @param ingress.server.hostname Default host for the ingress resource
    ##
    hostname: parse-server
    ## @param ingress.server.path Default path for the ingress resource*' in order to use this with ALB ingress controllers.
    ##
    path: /
    ## @param ingress.server.pathType Ingress path type
    ##
    pathType: Prefix
    ## @param ingress.server.extraHosts The list of additional hostnames to be covered with this ingress record.
    ## Most likely the hostname above will be enough, but in the event more hosts are needed, this is an array
    ## extraHosts:
    ## - name: parse.local
    ##   path: /
    ##
    extraHosts: []
    ## @param ingress.server.extraPaths Any additional arbitrary paths that may need to be added to the ingress under the main host.
    ## For example: The ALB ingress controller requires a special rule for handling SSL redirection.
    ## extraPaths:
    ## - path: /*
    ##   backend:
    ##     serviceName: ssl-redirect
    ##     servicePort: use-annotation
    ##
    extraPaths: []
  ## @param ingress.extraTls The tls configuration for additional hostnames to be covered with this ingress record.
  ## see: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls
  ## extraTls:
  ## - hosts:
  ##     - parse.local
  ##   secretName: parse.local-tls
  ##
  extraTls: []
  ## @param ingress.secrets If you're providing your own certificates, please use this to add the certificates as secrets
  ## key and certificate should start with -----BEGIN CERTIFICATE----- or
  ## -----BEGIN RSA PRIVATE KEY-----
  ##
  ## name should line up with a tlsSecret set further up
  ## If you're using cert-manager, this is unneeded, as it will create the secret for you if it is not set
  ##
  ## It is also possible to create and manage the certificates outside of this helm chart
  ## Please see README.md for more information
  ## e.g:
  ## - name: parse.local-tls
  ##   key:
  ##   certificate:
  ##
  secrets: []
  ## @param ingress.extraRules Additional rules to be covered with this ingress record
  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#ingress-rules
  ## e.g:
  ## extraRules:
  ## - host: parse.local
  ##     http:
  ##       path: /
  ##       backend:
  ##         service:
  ##           name: parse-svc
  ##           port:
  ##             name: http
  ##
  extraRules: []
## @section Persistence Parameters
##

## Enable persistence using Persistent Volume Claims
## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
##
persistence:
  ## @param persistence.enabled Enable Parse persistence using PVC
  ##
  enabled: false
  ## @param persistence.storageClass PVC Storage Class for Parse volume
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  storageClass: ""
  ## @param persistence.accessModes PVC Access Mode for Parse volume
  ##
  accessModes:
    - ReadWriteOnce
  ## @param persistence.size PVC Storage Request for Parse volume
  ##
  size: 8Gi
  ## @param persistence.existingClaim The name of an existing PVC to use for persistence
  ##
  existingClaim: ""
  ## @param persistence.selector Selector to match an existing Persistent Volume for Parse data PVC
  ## If set, the PVC can't have a PV dynamically provisioned for it
  ## E.g.
  ## selector:
  ##   matchLabels:
  ##     app: my-app
  ##
  selector: {}
  ## @param persistence.dataSource Custom PVC data source
  ##
  dataSource: {}
  ## @param persistence.annotations Persistent Volume Claim annotations
  ##
  annotations: {}
## @section Volume Permissions parameters
##

## Init containers parameters:
## volumePermissions: Change the owner and group of the persistent volume mountpoint to runAsUser:fsGroup values from the securityContext section.
##
volumePermissions:
  ## @param volumePermissions.enabled Enable init container that changes volume permissions in the data directory (for cases where the default k8s `runAsUser` and `fsUser` values do not work)
  ##
  enabled: false
  ## @param volumePermissions.image.registry [default: REGISTRY_NAME] Init container volume-permissions image registry
  ## @param volumePermissions.image.repository [default: REPOSITORY_NAME/os-shell] Init container volume-permissions image name
  ## @skip volumePermissions.image.tag Init container volume-permissions image tag
  ## @param volumePermissions.image.digest Init container volume-permissions image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param volumePermissions.image.pullPolicy Init container volume-permissions image pull policy
  ## @param volumePermissions.image.pullSecrets Init container volume-permissions image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/os-shell
    tag: 12-debian-12-r26
    digest: ""
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param volumePermissions.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if volumePermissions.resources is set (volumePermissions.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param volumePermissions.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
## ServiceAccount configuration
##
serviceAccount:
  ## @param serviceAccount.create Specifies whether a ServiceAccount should be created
  ##
  create: true
  ## @param serviceAccount.name The name of the ServiceAccount to use.
  ## If not set and create is true, a name is generated using the common.names.fullname template
  ##
  name: "parse"
  ## @param serviceAccount.annotations Additional Service Account annotations (evaluated as a template)
  ##
  annotations: {}
  ## @param serviceAccount.automountServiceAccountToken Automount service account token for the server service account
  ##
  automountServiceAccountToken: false
## @section MongoDB&reg; Parameters
##

## MongoDB&reg; chart configuration
## https://github.com/bitnami/charts/blob/main/bitnami/mongodb/values.yaml
##
mongodb:
  ## @param mongodb.enabled Enable MongoDB&reg; chart
  ## This is necessary for bitnami/common validation purposes. Setting enabled=false will
  ## make no difference in deploying the subchart. Change this when further standardizations
  ## are added (externalDB)
  ##
  enabled: false
  ## MongoDB&reg; Authentication parameters
  ##
  auth:
    ## @param mongodb.auth.enabled Enable MongoDB&reg; password authentication
    ## ref: https://docs.mongodb.com/manual/tutorial/enable-authentication/
    ##
    enabled: true
    ## @param mongodb.auth.rootPassword MongoDB&reg; admin password
    ## ref: https://github.com/bitnami/containers/tree/main/bitnami/mongodb#setting-the-root-password-on-first-run
    ##
    rootPassword: ""
    ## @param mongodb.auth.username MongoDB&reg; user
    ##
    username: bn_parse
    ## @param mongodb.auth.password MongoDB&reg; user password
    ##
    password: ""
    ## @param mongodb.auth.database MongoDB&reg; database
    ##
    database: bitnami_parse
  ## Enable persistence using Persistent Volume Claims
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  ##
  persistence:
    ## @param mongodb.persistence.enabled Enable MongoDB&reg; persistence using PVC
    ##
    enabled: false
    ## @param mongodb.persistence.storageClass PVC Storage Class for MongoDB&reg; volume
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack)
    ##
    storageClass: ""
    ## @param mongodb.persistence.accessMode PVC Access Mode for MongoDB&reg; volume
    ##
    accessMode: ReadWriteOnce
    ## @param mongodb.persistence.size PVC Storage Request for MongoDB&reg; volume
    ##
    size: 8Gi
  ## MongoDB(&reg;) containers' resource requests and limits.
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## We usually recommend not to specify default resources and to leave this as a conscious
  ## choice for the user. This also increases chances charts run on environments with little
  ## resources, such as Minikube. If you do want to specify resources, uncomment the following
  ## lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  ## @param mongodb.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if resources is set (resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "small"
  ## @param mongodb.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}

secretProviderClass:
  # Specifies whether a secrets provider class should be created
  create: false
  # Annotations to add to the secrets provider class
  annotations: {}
  # The name of the secrets provider class to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "parse"
  # Provide the secrets, full path name, list.
  secrets: []

database:
 host: ""
 name: parse
 port: 27017