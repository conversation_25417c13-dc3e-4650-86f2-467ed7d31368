{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and (include "mongodb.arbiter.enabled" .) .Values.arbiter.pdb.create }}
apiVersion: {{ include "common.capabilities.policy.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ printf "%s-arbiter" (include "mongodb.fullname" .) }}
  namespace: {{ include "mongodb.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: arbiter
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.arbiter.pdb.minAvailable }}
  minAvailable: {{ .Values.arbiter.pdb.minAvailable }}
  {{- end }}
  {{- if or .Values.arbiter.pdb.maxUnavailable (not .Values.arbiter.pdb.minAvailable) }}
  maxUnavailable: {{ .Values.arbiter.pdb.maxUnavailable | default 1 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.arbiter.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: arbiter
{{- end }}
