{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if (include "mongodb.createTlsSecret" .) }}
{{- $secretName := printf "%s" (include "mongodb.tlsSecretName" .) }}
{{- $fullname := include "mongodb.fullname" . }}
{{- $releaseNamespace := .Release.Namespace }}
{{- $clusterDomain := .Values.clusterDomain }}
{{- $cn := printf "%s.%s.svc.%s" $fullname .Release.Namespace $clusterDomain }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretName }}
  namespace: {{ template "mongodb.namespace" . }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: mongodb
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if or .Values.tls.caCert .Values.tls.caKey (not .Values.tls.autoGenerated) }}
  {{- $ca := buildCustomCert (required "A valid .Values.tls.caCert is required!" .Values.tls.caCert) (required "A valid .Values.tls.caKey is required!" .Values.tls.caKey) }}
  mongodb-ca-cert: {{ b64enc $ca.Cert }}
  mongodb-ca-key: {{ b64enc $ca.Key }}
  {{- else }}
  {{- $ca := genCA "myMongo-ca" 3650 }}
  mongodb-ca-cert: {{ include "common.secrets.lookup" (dict "secret" $secretName "key" "mongodb-ca-cert" "defaultValue" $ca.Cert "context" $) }}
  mongodb-ca-key: {{ include "common.secrets.lookup" (dict "secret" $secretName "key" "mongodb-ca-key" "defaultValue" $ca.Key "context" $) }}
  {{- end }}
{{- end }}
