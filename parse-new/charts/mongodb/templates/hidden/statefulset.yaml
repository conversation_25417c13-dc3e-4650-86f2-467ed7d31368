{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if (include "mongodb.hidden.enabled" .) }}
{{- $replicaCount := int .Values.hidden.replicaCount }}
{{- $loadBalancerIPListLength := len .Values.externalAccess.hidden.service.loadBalancerIPs }}
{{- if not (and .Values.externalAccess.hidden.enabled (not .Values.externalAccess.autoDiscovery.enabled) (not (eq $replicaCount $loadBalancerIPListLength )) (eq .Values.externalAccess.hidden.service.type "LoadBalancer")) }}
apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ printf "%s-hidden" (include "mongodb.fullname" .) }}
  namespace: {{ include "mongodb.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.hidden.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: hidden
  {{- if or .Values.hidden.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.hidden.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  serviceName: {{ printf "%s-hidden-headless" (include "mongodb.fullname" .) }}
  podManagementPolicy: {{ .Values.hidden.podManagementPolicy }}
  replicas: {{ .Values.hidden.replicaCount }}
  {{- if .Values.hidden.updateStrategy }}
  updateStrategy: {{- toYaml .Values.hidden.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.hidden.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: hidden
  template:
    metadata:
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: hidden
      {{- if or (include "mongodb.hidden.createConfigmap" .) .Values.hidden.podAnnotations }}
      annotations:
        {{- if (include "mongodb.hidden.createConfigmap" .) }}
        checksum/configuration: {{ include (print $.Template.BasePath "/hidden/configmap.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.hidden.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.hidden.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      {{- include "mongodb.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.hidden.schedulerName }}
      schedulerName: {{ .Values.hidden.schedulerName | quote }}
      {{- end }}
      serviceAccountName: {{ template "mongodb.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.hidden.automountServiceAccountToken }}
      {{- if .Values.hidden.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.hidden.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.hidden.podAffinityPreset "component" "hidden" "customLabels" $podLabels "topologyKey" .Values.topologyKey "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.hidden.podAntiAffinityPreset "component" "hidden" "customLabels" $podLabels "topologyKey" .Values.topologyKey "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.hidden.nodeAffinityPreset.type "key" .Values.hidden.nodeAffinityPreset.key "values" .Values.hidden.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.hidden.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.hidden.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.hidden.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.topologySpreadConstraints "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.hidden.priorityClassName }}
      priorityClassName: {{ .Values.hidden.priorityClassName }}
      {{- end }}
      {{- if .Values.hidden.runtimeClassName }}
      runtimeClassName: {{ .Values.hidden.runtimeClassName }}
      {{- end }}
      {{- if .Values.hidden.podSecurityContext.enabled }}
      securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.hidden.podSecurityContext "context" $) | nindent 8 }}
      {{- end }}
      {{ if .Values.hidden.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.hidden.terminationGracePeriodSeconds }}
      {{- end }}
      enableServiceLinks: {{ .Values.enableServiceLinks }}
      initContainers:
        {{- if .Values.hidden.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.hidden.initContainers "context" $) | nindent 8 }}
        {{- end }}
        {{- if and .Values.volumePermissions.enabled .Values.hidden.persistence.enabled }}
        {{- include "mongodb.initContainer.volumePermissions" . | indent 8 }}
        {{- end }}
        {{- if and .Values.externalAccess.hidden.enabled .Values.externalAccess.autoDiscovery.enabled (eq .Values.externalAccess.hidden.service.type "LoadBalancer") }}
        {{- include "mongodb.initContainers.autoDiscovery" . | indent 8 }}
        {{- end }}
        {{- if and .Values.externalAccess.enabled ( or .Values.externalAccess.service.publicNames  .Values.externalAccess.service.domain ) }}
        {{- include "mongodb.initContainers.dnsCheck" . | indent 8 }}
        {{- end }}
        {{- include "mongodb.initContainer.prepareLogDir" . | nindent 8 }}
        {{- if .Values.tls.enabled }}
        - name: generate-tls-certs
          image: {{ include "mongodb.tls.image" . }}
          imagePullPolicy: {{ .Values.tls.image.pullPolicy | quote }}
          env:
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: MY_POD_HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          volumeMounts:
            {{- if (include "mongodb.autoGenerateCerts" .) }}
            - name: certs-volume
              mountPath: /certs/CAs
            {{- else }}
            {{- range $index, $_ := .Values.tls.hidden.existingSecrets }}
            - name: mongodb-certs-{{ $index }}
              mountPath: /certs-{{ $index }}
            {{- end }}
            {{- end }}
            - name: certs
              mountPath: /certs
            - name: common-scripts
              mountPath: /bitnami/scripts
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
          command:
            - /bitnami/scripts/generate-certs.sh
          args:
            - -s {{ printf "%s-hidden-headless" (include "mongodb.fullname" .) }}
            {{- if .Values.externalAccess.hidden.service.loadBalancerIPs }}
            - -i {{ join "," .Values.externalAccess.hidden.service.loadBalancerIPs }}
            {{- end }}
            {{- if or .Values.tls.extraDnsNames .Values.externalAccess.service.publicNames }}
            - -n {{ join "," ( concat  .Values.tls.extraDnsNames .Values.externalAccess.service.publicNames ) }}
            {{- end }}
          {{- if .Values.tls.resources }}
          resources: {{- toYaml .Values.tls.resources | nindent 12 }}
          {{- else if ne .Values.tls.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.tls.resourcesPreset) | nindent 12 }}
          {{- end }}
        {{- end }}
      containers:
        - name: mongodb
          image: {{ include "mongodb.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.hidden.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.hidden.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.hidden.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - /scripts/setup-hidden.sh
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.hidden.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.args "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.hidden.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            {{- if and .Values.externalAccess.hidden.enabled .Values.externalAccess.autoDiscovery.enabled (eq .Values.externalAccess.hidden.service.type "LoadBalancer") }}
            - name: SHARED_FILE
              value: "/shared/info.txt"
            {{- end }}
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: MY_POD_HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: K8S_SERVICE_NAME
              value: "{{ include "mongodb.service.nameOverride" . }}"
            - name: K8S_HIDDEN_NODE_SERVICE_NAME
              value: "{{ include "mongodb.fullname" . }}-hidden-headless"
            - name: MONGODB_REPLICA_SET_MODE
              value: "hidden"
            - name: MONGODB_INITIAL_PRIMARY_HOST
              value: {{ include "mongodb.initialPrimaryHost" . | quote }}
            - name: MONGODB_REPLICA_SET_NAME
              value: {{ .Values.replicaSetName | quote }}
            {{- if and .Values.replicaSetHostnames (not .Values.externalAccess.hidden.enabled) }}
            - name: MONGODB_ADVERTISED_HOSTNAME
              value: "$(MY_POD_NAME).$(K8S_HIDDEN_NODE_SERVICE_NAME).$(MY_POD_NAMESPACE).svc.{{ .Values.clusterDomain }}"
            {{- end }}
            {{- $customUsers := include "mongodb.customUsers" . -}}
            {{- $customDatabases := include "mongodb.customDatabases" . -}}
            {{- if not (empty $customUsers) }}
            - name: MONGODB_EXTRA_USERNAMES
              value: {{ $customUsers | quote }}
            {{- end }}
            {{- if not (empty $customDatabases) }}
            - name: MONGODB_EXTRA_DATABASES
              value: {{ $customDatabases | quote }}
            {{- end }}
            {{- if .Values.auth.enabled }}
            {{- if and (not (empty $customUsers)) (not (empty $customDatabases)) }}
            - name: MONGODB_EXTRA_PASSWORDS
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-passwords
            {{- end }}
            - name: MONGODB_ROOT_USER
              value: {{ .Values.auth.rootUser | quote }}
            - name: MONGODB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-root-password
            - name: MONGODB_REPLICA_SET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-replica-set-key
            {{- end }}
            {{- if and .Values.metrics.enabled (not (empty .Values.metrics.username)) }}
            - name: MONGODB_METRICS_USERNAME
              value: {{ .Values.metrics.username | quote }}
            {{- if .Values.auth.enabled }}
            - name: MONGODB_METRICS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-metrics-password
            {{- end }}
            {{- end }}
            - name: ALLOW_EMPTY_PASSWORD
              value: {{ ternary "no" "yes" .Values.auth.enabled | quote }}
            - name: MONGODB_SYSTEM_LOG_VERBOSITY
              value: {{ .Values.systemLogVerbosity | quote }}
            - name: MONGODB_DISABLE_SYSTEM_LOG
              value: {{ ternary "yes" "no" .Values.disableSystemLog | quote }}
            - name: MONGODB_DISABLE_JAVASCRIPT
              value: {{ ternary "yes" "no" .Values.disableJavascript | quote }}
            - name: MONGODB_ENABLE_JOURNAL
              value: {{ ternary "yes" "no" .Values.enableJournal | quote }}
            - name: MONGODB_PORT_NUMBER
              value: {{ .Values.hidden.containerPorts.mongodb | quote }}
            - name: MONGODB_ENABLE_IPV6
              value: {{ ternary "yes" "no" .Values.enableIPv6 | quote }}
            - name: MONGODB_ENABLE_DIRECTORY_PER_DB
              value: {{ ternary "yes" "no" .Values.directoryPerDB | quote }}
            {{- $extraFlags := .Values.hidden.extraFlags | join " " -}}
            {{- if .Values.tls.enabled }}
              {{- if .Values.tls.mTLS.enabled }}
              {{- $extraFlags = printf "--tlsCAFile=/certs/mongodb-ca-cert %s" $extraFlags  }}
              {{- end }}
              {{- $extraFlags = printf "--tlsMode=%s --tlsCertificateKeyFile=/certs/mongodb.pem %s" .Values.tls.mode $extraFlags  }}
            {{- end }}
            {{- if ne $extraFlags "" }}
            - name: MONGODB_EXTRA_FLAGS
              value: {{ $extraFlags | quote }}
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: MONGODB_CLIENT_EXTRA_FLAGS
              value: --tls {{ if .Values.tls.mTLS.enabled }}--tlsCertificateKeyFile=/certs/mongodb.pem {{ end }}--tlsCAFile=/certs/mongodb-ca-cert
            {{- end }}
            {{- if .Values.hidden.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.hidden.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          {{- if or .Values.hidden.extraEnvVarsCM .Values.hidden.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.hidden.extraEnvVarsCM }}
            - configMapRef:
                name: {{ tpl .Values.hidden.extraEnvVarsCM . | quote }}
            {{- end }}
            {{- if .Values.hidden.extraEnvVarsSecret }}
            - secretRef:
                name: {{ tpl .Values.hidden.extraEnvVarsSecret . | quote }}
            {{- end }}
          {{- end }}
          ports:
            - containerPort: {{ .Values.hidden.containerPorts.mongodb }}
              name: mongodb
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.hidden.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.hidden.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.hidden.livenessProbe "enabled") "context" $) | nindent 12 }}
            exec:
              command:
                - /bitnami/scripts/ping-mongodb.sh
          {{- end }}
          {{- if .Values.hidden.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.hidden.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.hidden.readinessProbe "enabled") "context" $) | nindent 12 }}
            exec:
              command:
                - /bitnami/scripts/readiness-probe.sh
          {{- end }}
          {{- if .Values.hidden.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.hidden.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.hidden.startupProbe "enabled") "context" $) | nindent 12 }}
            exec:
              command:
                - /bitnami/scripts/startup-probe.sh
          {{- end }}
          {{- end }}
          {{- if .Values.hidden.resources }}
          resources: {{- toYaml .Values.hidden.resources | nindent 12 }}
          {{- else if ne .Values.hidden.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.hidden.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: datadir
              mountPath: {{ .Values.hidden.persistence.mountPath }}
              subPath: {{ .Values.hidden.persistence.subPath }}
            - name: common-scripts
              mountPath: /bitnami/scripts
            {{- if or .Values.initdbScriptsConfigMap .Values.initdbScripts }}
            - name: custom-init-scripts
              mountPath: /docker-entrypoint-initdb.d
            {{- end }}
            {{- if or .Values.hidden.configuration .Values.hidden.existingConfigmap }}
            - name: config
              mountPath: /opt/bitnami/mongodb/conf/mongodb.conf
              subPath: mongodb.conf
            {{- end }}
            - name: scripts
              mountPath: /scripts/setup-hidden.sh
              subPath: setup-hidden.sh
            {{- if and .Values.externalAccess.hidden.enabled .Values.externalAccess.autoDiscovery.enabled (eq .Values.externalAccess.hidden.service.type "LoadBalancer") }}
            - name: shared
              mountPath: /shared
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: certs
              mountPath: /certs
            {{- end }}
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/mongodb/conf
              subPath: app-conf-dir
            - name: empty-dir
              mountPath: /opt/bitnami/mongodb/tmp
              subPath: app-tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/mongodb/logs
              subPath: app-logs-dir
            - name: empty-dir
              mountPath: /.mongodb
              subPath: mongosh-home
            {{- if .Values.hidden.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.hidden.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
        {{- if .Values.metrics.enabled }}
        - name: metrics
          image: {{ template "mongodb.metrics.image" . }}
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.metrics.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - /bin/bash
            - -ec
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.metrics.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.args "context" $) | nindent 12 }}
          {{- else }}
          args:
            - |
              /bin/mongodb_exporter {{ include "mongodb.exporterArgs" $ }} --mongodb.direct-connect --mongodb.global-conn-pool --mongodb.uri "{{ include "mongodb.mongodb_exporter.uri" . }}" {{ .Values.metrics.extraFlags }}
          {{- end }}
          env:
            {{- if .Values.auth.enabled }}
            {{- if not .Values.metrics.username }}
            - name: MONGODB_ROOT_USER
              value: {{ .Values.auth.rootUser | quote }}
            - name: MONGODB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-root-password
            {{- else }}
            - name: MONGODB_METRICS_USERNAME
              value: {{ .Values.metrics.username | quote }}
            - name: MONGODB_METRICS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-metrics-password
            {{- end }}
            {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            {{- if .Values.tls.enabled }}
            - name: certs
              mountPath: /certs
            {{- end }}
            - name: empty-dir
              mountPath: /opt/bitnami/redis-cluster/tmp
              subPath: app-tmp-dir
            {{- if .Values.metrics.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.metrics.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
          ports:
            - name: metrics
              containerPort: 9216
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.metrics.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.livenessProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: metrics
          {{- end }}
          {{- if .Values.metrics.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /
              port: metrics
          {{- end }}
          {{- if .Values.metrics.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.startupProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: metrics
          {{- end }}
          {{- end }}
          {{- if .Values.metrics.resources }}
          resources: {{- toYaml .Values.metrics.resources | nindent 12 }}
          {{- else if ne .Values.metrics.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.metrics.resourcesPreset) | nindent 12 }}
          {{- end }}
        {{- end }}
        {{- if .Values.hidden.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.hidden.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        - name: empty-dir
          emptyDir: {}
        - name: common-scripts
          configMap:
            name: {{ printf "%s-common-scripts" (include "mongodb.fullname" .) }}
            defaultMode: 0555
        {{- if or .Values.initdbScriptsConfigMap .Values.initdbScripts }}
        - name: custom-init-scripts
          configMap:
            name: {{ template "mongodb.initdbScriptsCM" . }}
        {{- end }}
        {{- if or .Values.hidden.configuration .Values.hidden.existingConfigmap }}
        - name: config
          configMap:
            name: {{ include "mongodb.hidden.configmapName" . }}
        {{- end }}
        {{- if and .Values.externalAccess.hidden.enabled .Values.externalAccess.autoDiscovery.enabled (eq .Values.externalAccess.hidden.service.type "LoadBalancer") }}
        - name: shared
          emptyDir: {}
        {{- end }}
        - name: scripts
          configMap:
            name: {{ printf "%s-scripts" (include "mongodb.fullname" .) }}
            defaultMode: 0755
        {{- if .Values.hidden.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.hidden.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
        {{- if .Values.tls.enabled }}
        - name: certs
          emptyDir: {}
        {{- if (include "mongodb.autoGenerateCerts" .) }}
        - name: certs-volume
          secret:
            secretName: {{ template "mongodb.tlsSecretName" . }}
            items:
            - key: mongodb-ca-cert
              path: mongodb-ca-cert
              mode: 0600
            - key: mongodb-ca-key
              path: mongodb-ca-key
              mode: 0600
        {{- else }}
        {{- range $index, $secret := .Values.tls.hidden.existingSecrets }}
        - name: mongodb-certs-{{ $index }}
          secret:
            secretName: {{ include "common.tplvalues.render" ( dict "value" $secret "context" $) }}
            defaultMode: 256
        {{- end }}
        {{- end }}
        {{- end }}
  {{- if not .Values.hidden.persistence.enabled }}
        - name: datadir
          {{- if .Values.hidden.persistence.medium }}
          emptyDir:
            medium: {{ .Values.hidden.persistence.medium | quote }}
          {{- else }}
          emptyDir: {}
          {{- end }}
  {{- else }}
  volumeClaimTemplates:
    - metadata:
        name: datadir
        {{- if .Values.hidden.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.persistence.annotations "context" $) | nindent 10 }}
        {{- end }}
        {{- if .Values.persistence.labels }}
        labels: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.labels "context" $) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.hidden.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.hidden.persistence.size | quote }}
            {{- if .Values.hidden.persistence.volumeClaimTemplates.requests }}
            {{- include "common.tplvalues.render" (dict "value" .Values.hidden.persistence.volumeClaimTemplates.requests "context" $) | nindent 12 }}
            {{- end }}
        {{- if .Values.hidden.persistence.volumeClaimTemplates.dataSource }}
        dataSource: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.persistence.volumeClaimTemplates.dataSource "context" $) | nindent 10 }}
        {{- end }}
        {{- if .Values.hidden.persistence.volumeClaimTemplates.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.hidden.persistence.volumeClaimTemplates.selector "context" $) | nindent 10 }}
        {{- end }}
        {{ include "common.storage.class" (dict "persistence" .Values.hidden.persistence "global" .Values.global) }}
  {{- end }}
{{- end }}
{{- end }}
