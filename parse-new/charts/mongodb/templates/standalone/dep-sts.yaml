{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if not (eq .Values.architecture "replicaset") }}
apiVersion: {{ if .Values.useStatefulSet }}{{ include "common.capabilities.statefulset.apiVersion" . }}{{- else }}{{ include "common.capabilities.deployment.apiVersion" . }}{{- end }}
kind: {{ if .Values.useStatefulSet }}StatefulSet{{- else }}Deployment{{- end }}
metadata:
  name: {{ include "mongodb.fullname" . }}
  namespace: {{ include "mongodb.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: mongodb
  {{- if or .Values.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ gt (.Values.replicaCount | int) 1 | ternary 1 .Values.replicaCount }}
  {{- if .Values.useStatefulSet }}
  serviceName: {{ include "mongodb.service.nameOverride" . }}
  {{- end }}
  {{- if .Values.updateStrategy}}
  {{- if .Values.useStatefulSet }}
  updateStrategy:
  {{- else }}
  strategy:
  {{- end }}
    {{- toYaml .Values.updateStrategy | nindent 4 }}
  {{- end}}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: mongodb
  template:
    metadata:
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: mongodb
      {{- if or (include "mongodb.createConfigmap" .) .Values.podAnnotations }}
      annotations:
        {{- if (include "mongodb.createConfigmap" .) }}
        checksum/configuration: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      {{- include "mongodb.imagePullSecrets" . | nindent 6 }}
      automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
      {{- if .Values.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName | quote }}
      {{- end }}
      serviceAccountName: {{ template "mongodb.serviceAccountName" . }}
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAffinityPreset "component" "mongodb" "customLabels" $podLabels "topologyKey" .Values.topologyKey "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAntiAffinityPreset "component" "mongodb" "customLabels" $podLabels "topologyKey" .Values.topologyKey "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.nodeAffinityPreset.type "key" .Values.nodeAffinityPreset.key "values" .Values.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
      {{- if .Values.runtimeClassName }}
      runtimeClassName: {{ .Values.runtimeClassName }}
      {{- end }}
      {{- if .Values.podSecurityContext.enabled }}
      securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.podSecurityContext "context" $) | nindent 8 }}
      {{- end }}
      {{ if .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      {{- end }}
      enableServiceLinks: {{ .Values.enableServiceLinks }}
      initContainers:
        {{- if .Values.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.initContainers "context" $) | nindent 8 }}
        {{- end }}
        {{- if and .Values.volumePermissions.enabled .Values.persistence.enabled }}
        {{- include "mongodb.initContainer.volumePermissions" . | indent 8 }}
        {{- end }}
        {{- include "mongodb.initContainer.prepareLogDir" . | nindent 8 }}
        {{- if .Values.tls.enabled }}
        - name: generate-tls-certs
          image: {{ include "mongodb.tls.image" . }}
          imagePullPolicy: {{ .Values.tls.image.pullPolicy | quote }}
          env:
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: MY_POD_HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            {{- if (include "mongodb.autoGenerateCerts" .) }}
            - name: certs-volume
              mountPath: /certs/CAs
            {{- else }}
            - name: mongodb-certs-0
              mountPath: /certs-0
            {{- end }}
            - name: certs
              mountPath: /certs
            - name: common-scripts
              mountPath: /bitnami/scripts
          command:
            - /bitnami/scripts/generate-certs.sh
          args:
            - -s {{ include "mongodb.service.nameOverride" . }}
            {{- if .Values.externalAccess.service.loadBalancerIPs }}
            - -i {{ join "," .Values.externalAccess.service.loadBalancerIPs }}
            {{- end }}
            {{- if or .Values.tls.extraDnsNames .Values.externalAccess.service.publicNames }}
            - -n {{ join "," ( concat .Values.tls.extraDnsNames .Values.externalAccess.service.publicNames ) }}
            {{- end }}
          {{- if .Values.tls.resources }}
          resources: {{- toYaml .Values.tls.resources | nindent 12 }}
          {{- else if ne .Values.tls.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.tls.resourcesPreset) | nindent 12 }}
          {{- end }}
          {{- if .Values.tls.securityContext }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.tls.securityContext "context" $) | nindent 12 }}
          {{- end }}
        {{- end }}
      containers:
        - name: mongodb
          image: {{ include "mongodb.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.args "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            {{- $customUsers := include "mongodb.customUsers" . -}}
            {{- $customDatabases := include "mongodb.customDatabases" . -}}
            {{- if not (empty $customUsers) }}
            - name: MONGODB_EXTRA_USERNAMES
              value: {{ $customUsers | quote }}
            {{- end }}
            {{- if not (empty $customDatabases) }}
            - name: MONGODB_EXTRA_DATABASES
              value: {{ $customDatabases | quote }}
            {{- end }}
            {{- if .Values.auth.enabled }}
            {{- if and (not (empty $customUsers)) (not (empty $customDatabases)) }}
            - name: MONGODB_EXTRA_PASSWORDS
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-passwords
            {{- end }}
            - name: MONGODB_ROOT_USER
              value: {{ .Values.auth.rootUser | quote }}
            - name: MONGODB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-root-password
            {{- end }}
            {{- if and .Values.metrics.enabled (not (empty .Values.metrics.username)) }}
            - name: MONGODB_METRICS_USERNAME
              value: {{ .Values.metrics.username | quote }}
            {{- if .Values.auth.enabled }}
            - name: MONGODB_METRICS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-metrics-password
            {{- end }}
            {{- end }}
            - name: ALLOW_EMPTY_PASSWORD
              value: {{ ternary "no" "yes" .Values.auth.enabled | quote }}
            - name: MONGODB_SYSTEM_LOG_VERBOSITY
              value: {{ .Values.systemLogVerbosity | quote }}
            - name: MONGODB_DISABLE_SYSTEM_LOG
              value: {{ ternary "yes" "no" .Values.disableSystemLog | quote }}
            - name: MONGODB_DISABLE_JAVASCRIPT
              value: {{ ternary "yes" "no" .Values.disableJavascript | quote }}
            - name: MONGODB_ENABLE_JOURNAL
              value: {{ ternary "yes" "no" .Values.enableJournal | quote  }}
            - name: MONGODB_PORT_NUMBER
              value: {{ .Values.containerPorts.mongodb | quote }}
            - name: MONGODB_ENABLE_IPV6
              value: {{ ternary "yes" "no" .Values.enableIPv6 | quote }}
            - name: MONGODB_ENABLE_DIRECTORY_PER_DB
              value: {{ ternary "yes" "no" .Values.directoryPerDB | quote }}
            {{- $extraFlags := .Values.extraFlags | join " " -}}
            {{- if .Values.tls.enabled }}
              {{- if .Values.tls.mTLS.enabled }}
              {{- $extraFlags = printf "--tlsCAFile=/certs/mongodb-ca-cert %s" $extraFlags  }}
              {{- end }}
              {{- $extraFlags = printf "--tlsMode=%s --tlsCertificateKeyFile=/certs/mongodb.pem %s" .Values.tls.mode $extraFlags  }}
            {{- end }}
            {{- if ne $extraFlags "" }}
            - name: MONGODB_EXTRA_FLAGS
              value: {{ $extraFlags | quote }}
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: MONGODB_CLIENT_EXTRA_FLAGS
              value: --tls {{ if .Values.tls.mTLS.enabled }}--tlsCertificateKeyFile=/certs/mongodb.pem {{ end }}--tlsCAFile=/certs/mongodb-ca-cert
            {{- end }}
            {{- if .Values.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          {{- if or .Values.extraEnvVarsCM .Values.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.extraEnvVarsCM }}
            - configMapRef:
                name: {{ tpl .Values.extraEnvVarsCM . | quote }}
            {{- end }}
            {{- if .Values.extraEnvVarsSecret }}
            - secretRef:
                name: {{ tpl .Values.extraEnvVarsSecret . | quote }}
            {{- end }}
          {{- end }}
          ports:
            - name: mongodb
              containerPort: {{ .Values.containerPorts.mongodb }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.livenessProbe "enabled") "context" $) | nindent 12 }}
            exec:
              command:
                - /bitnami/scripts/ping-mongodb.sh
          {{- end }}
          {{- end }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.readinessProbe "enabled") "context" $) | nindent 12 }}
            exec:
              command:
                - /bitnami/scripts/readiness-probe.sh
          {{- end }}
          {{- end }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.startupProbe "enabled") "context" $) | nindent 12 }}
            exec:
              command:
                - /bitnami/scripts/startup-probe.sh
          {{- end }}
          {{- end }}
          {{- if .Values.resources }}
          resources: {{- toYaml .Values.resources | nindent 12 }}
          {{- else if ne .Values.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/mongodb/conf
              subPath: app-conf-dir
            - name: empty-dir
              mountPath: /opt/bitnami/mongodb/tmp
              subPath: app-tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/mongodb/logs
              subPath: app-logs-dir
            - name: empty-dir
              mountPath: /.mongodb
              subPath: mongosh-home
            - name: {{ .Values.persistence.name | default "datadir" }}
              mountPath: {{ .Values.persistence.mountPath }}
              subPath: {{ .Values.persistence.subPath }}
            - name: common-scripts
              mountPath: /bitnami/scripts
            {{- if or .Values.initdbScriptsConfigMap .Values.initdbScripts }}
            - name: custom-init-scripts
              mountPath: /docker-entrypoint-initdb.d
            {{- end }}
            {{- if or .Values.configuration .Values.existingConfigmap }}
            - name: config
              mountPath: /opt/bitnami/mongodb/conf/mongodb.conf
              subPath: mongodb.conf
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: certs
              mountPath: /certs
            {{- end }}
            {{- if .Values.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
        {{- if .Values.metrics.enabled }}
        - name: metrics
          image: {{ template "mongodb.metrics.image" . }}
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.metrics.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - /bin/bash
            - -ec
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.metrics.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.args "context" $) | nindent 12 }}
          {{- else }}
          args:
            - |
              /bin/mongodb_exporter {{ include "mongodb.exporterArgs" $ }} --mongodb.direct-connect --mongodb.global-conn-pool --web.listen-address ":{{ .Values.metrics.containerPort }}" --mongodb.uri "{{ include "mongodb.mongodb_exporter.uri" . }}" {{ .Values.metrics.extraFlags }}
          {{- end }}
          env:
            {{- if .Values.auth.enabled }}
            {{- if not .Values.metrics.username }}
            - name: MONGODB_ROOT_USER
              value: {{ .Values.auth.rootUser | quote }}
            - name: MONGODB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-root-password
            {{- else }}
            - name: MONGODB_METRICS_USERNAME
              value: {{ .Values.metrics.username | quote }}
            - name: MONGODB_METRICS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "mongodb.secretName" . }}
                  key: mongodb-metrics-password
            {{- end }}
            {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            {{- if .Values.tls.enabled }}
            - name: certs
              mountPath: /certs
            {{- end }}
            {{- if .Values.metrics.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.metrics.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
          ports:
            - name: metrics
              containerPort: {{ .Values.metrics.containerPort }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.metrics.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.livenessProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: metrics
          {{- end }}
          {{- if .Values.metrics.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /
              port: metrics
          {{- end }}
          {{- if .Values.metrics.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.metrics.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.metrics.startupProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: metrics
          {{- end }}
          {{- end }}
          {{- if .Values.metrics.resources }}
          resources: {{- toYaml .Values.metrics.resources | nindent 12 }}
          {{- else if ne .Values.metrics.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.metrics.resourcesPreset) | nindent 12 }}
          {{- end }}
        {{- end }}
        {{- if .Values.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        - name: empty-dir
          emptyDir: {}
        - name: common-scripts
          configMap:
            name: {{ printf "%s-common-scripts" (include "mongodb.fullname" .) }}
            defaultMode: 0550
        {{- if or .Values.initdbScriptsConfigMap .Values.initdbScripts }}
        - name: custom-init-scripts
          configMap:
            name: {{ template "mongodb.initdbScriptsCM" . }}
        {{- end }}
        {{- if or .Values.configuration .Values.existingConfigmap }}
        - name: config
          configMap:
            name: {{ include "mongodb.configmapName" . }}
        {{- end }}
        {{- if .Values.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
        {{- if .Values.tls.enabled }}
        - name: certs
          emptyDir: {}
        {{- if (include "mongodb.autoGenerateCerts" .) }}
        - name: certs-volume
          secret:
            secretName: {{ template "mongodb.tlsSecretName" . }}
            items:
            - key: mongodb-ca-cert
              path: mongodb-ca-cert
              mode: 0600
            - key: mongodb-ca-key
              path: mongodb-ca-key
              mode: 0600
        {{- else }}
        - name: mongodb-certs-0
          secret:
            secretName: {{ include "common.tplvalues.render" ( dict "value" .Values.tls.standalone.existingSecret "context" $) }}
            defaultMode: 256
        {{- end }}
        {{- end }}
  {{- if not .Values.persistence.enabled }}
        - name: {{ .Values.persistence.name | default "datadir" }}
          {{- if .Values.persistence.medium }}
          emptyDir:
            medium: {{ .Values.persistence.medium | quote }}
          {{- else }}
          emptyDir: {}
          {{- end }}
  {{- else if .Values.persistence.existingClaim }}
        - name: {{ .Values.persistence.name | default "datadir" }}
          persistentVolumeClaim:
            claimName: {{ printf "%s" (tpl .Values.persistence.existingClaim .) }}
  {{- else if not .Values.useStatefulSet }}
        - name: {{ .Values.persistence.name | default "datadir" }}
          persistentVolumeClaim:
            claimName: {{ template "mongodb.fullname" . }}
  {{- else }}
  {{- if .Values.persistentVolumeClaimRetentionPolicy.enabled }}
  persistentVolumeClaimRetentionPolicy:
    whenDeleted: {{ .Values.persistentVolumeClaimRetentionPolicy.whenDeleted }}
    whenScaled: {{ .Values.persistentVolumeClaimRetentionPolicy.whenScaled }}
  {{- end }}
  volumeClaimTemplates:
    - metadata:
        name: {{ .Values.persistence.name | default "datadir" }}
        {{- if .Values.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.annotations "context" $) | nindent 10 }}
        {{- end }}
        {{- if .Values.persistence.labels }}
        labels: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.labels "context" $) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
        {{- if .Values.persistence.volumeClaimTemplates.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.volumeClaimTemplates.selector "context" $) | nindent 10 }}
        {{- end }}
        {{ include "common.storage.class" (dict "persistence" .Values.persistence "global" .Values.global) }}
  {{- end }}
{{- end }}
