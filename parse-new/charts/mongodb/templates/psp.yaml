{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and (include "common.capabilities.psp.supported" .) .Values.podSecurityPolicy.create }}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ include "mongodb.fullname" . }}
  namespace: {{ include "mongodb.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.podSecurityPolicy.spec }}
{{ include "common.tplvalues.render" ( dict "value" .Values.podSecurityPolicy.spec "context" $ ) | nindent 2 }}
{{- else }}
  allowPrivilegeEscalation: {{ .Values.podSecurityPolicy.allowPrivilegeEscalation }}
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: {{ .Values.podSecurityContext.fsGroup }}
        max: {{ .Values.podSecurityContext.fsGroup }}
  hostIPC: false
  hostNetwork: false
  hostPID: false
  privileged: {{ .Values.podSecurityPolicy.privileged }}
  readOnlyRootFilesystem: false
  requiredDropCapabilities:
    - ALL
  runAsUser:
    rule: 'MustRunAs'
    ranges:
      - min: {{ .Values.containerSecurityContext.runAsUser }}
        max: {{ .Values.containerSecurityContext.runAsUser }}
  seLinux:
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: {{ .Values.containerSecurityContext.runAsUser }}
        max: {{ .Values.containerSecurityContext.runAsUser }}
  volumes:
    - 'configMap'
    - 'secret'
    - 'emptyDir'
    - 'persistentVolumeClaim'
{{- end }}
{{- end }}
