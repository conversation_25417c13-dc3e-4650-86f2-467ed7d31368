{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.rbac.create }}
apiVersion: {{ include "common.capabilities.rbac.apiVersion" . }}
kind: Role
metadata:
  name: {{ include "mongodb.fullname" . }}
  namespace: {{ include "mongodb.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
rules:
  - apiGroups:
      - ""
    resources:
      - services
    verbs:
      - get
      - list
      - watch
{{- if .Values.rbac.rules }}
{{- include "common.tplvalues.render" ( dict "value" .Values.rbac.rules "context" $ ) | nindent 2 }}
{{- end -}}
{{- if and (include "common.capabilities.psp.supported" .) .Values.podSecurityPolicy.create }}
  - apiGroups: ['{{ template "podSecurityPolicy.apiGroup" . }}']
    resources: ['podsecuritypolicies']
    verbs: ['use']
    resourceNames: [{{ include "mongodb.fullname" . }}]
{{- end -}}
{{- end }}
