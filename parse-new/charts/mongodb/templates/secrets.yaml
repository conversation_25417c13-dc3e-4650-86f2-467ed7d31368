{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.auth.enabled }}
{{- $replicaCount := int .Values.replicaCount }}
{{- $port := .Values.service.ports.mongodb }}
{{- $host := include "mongodb.service.nameOverride" . }}
{{- $hostForURI := printf "%s:%s" (include "mongodb.service.nameOverride" .) (print $port) }}
{{- if (eq .Values.architecture "replicaset") }}
  {{- $fullname := include "mongodb.fullname" . }}
  {{- $releaseNamespace := include "mongodb.namespace" . }}
  {{- $clusterDomain := .Values.clusterDomain }}
  {{- $mongoList := list }}
  {{- $mongoOnlyHostList := list }}
  {{- range $e, $i := until $replicaCount }}
  {{- $mongoOnlyHostList = append $mongoList (printf "%s-%d.%s-headless.%s.svc.%s" $fullname $i $fullname $releaseNamespace $clusterDomain) }}
  {{- $mongoList = append $mongoList (printf "%s-%d.%s-headless.%s.svc.%s:%s" $fullname $i $fullname $releaseNamespace $clusterDomain (print $port)) }}
  {{- end }}
  {{- $host = (join "," $mongoOnlyHostList) }}
  {{- $hostForURI = (join "," $mongoList) }}
{{- end }}

{{/* Root user section.  */}}
{{- $rootPassword := include "common.secrets.passwords.manage" (dict "secret" (include "mongodb.secretName" .) "key" "mongodb-root-password" "providedValues" (list "auth.rootPassword" ) "context" $) | trimAll "\"" | b64dec }}

{{/* Custom user section. This chart allows creating multiple users */}}
{{- $customUsers := include "mongodb.customUsers" . }}
{{- $customDatabases := include "mongodb.customDatabases" . }}
{{- $customPasswords := include "mongodb.customPasswords" . }}
{{- $passwords := "" }}
{{- $passwordList := list -}}
{{- $customUsersList := list }}
{{- $customDatabasesList := list }}
{{- $customPasswordsList := list }}
{{- if and (not (empty $customUsers)) (not (empty $customDatabases)) }}
{{- $customUsersList = splitList "," $customUsers }}
{{- $customDatabasesList = splitList "," $customDatabases }}
{{- if not (empty $customPasswords) }}
{{- $passwordList = $customPasswords }}
{{- $customPasswordsList = splitList "," $customPasswords }}
{{- else }}
{{- range $customUsersList }}
{{- $customPasswordsList = append $customPasswordsList (randAlphaNum 10) }}
{{- end -}}
{{- $passwordList = (join "," $customPasswordsList) }}
{{- end }}
{{- $passwords = include "common.secrets.passwords.manage" (dict "secret" (include "mongodb.secretName" .) "key" "mongodb-passwords" "providedValues" (list "mongodbPasswords") "context" (set (deepCopy $) "Values" (dict "mongodbPasswords" $passwordList))) | trimAll "\"" | b64dec }}
{{- end }}

{{- if (include "mongodb.createSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "mongodb.fullname" . }}
  namespace: {{ template "mongodb.namespace" . }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: mongodb
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  mongodb-root-password: {{ print $rootPassword | b64enc | quote }}
  {{- if and (not (empty $customUsers)) (not (empty $customDatabases)) }}
  mongodb-passwords: {{ print $passwords | b64enc | quote }}
  {{- end }}
  {{- if .Values.metrics.username }}
  mongodb-metrics-password: {{ include "common.secrets.passwords.manage" (dict "secret" (include "mongodb.fullname" .) "key" "mongodb-metrics-password" "providedValues" (list "metrics.password" ) "context" $) }}
  {{- end }}
  {{- if eq .Values.architecture "replicaset" }}
  mongodb-replica-set-key: {{ include "common.secrets.passwords.manage" (dict "secret" (include "mongodb.fullname" .) "key" "mongodb-replica-set-key" "providedValues" (list "auth.replicaSetKey" ) "context" $) }}
  {{- end }}
{{- end }}
{{- if .Values.serviceBindings.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind-root
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/mongodb
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "mongodb" | b64enc | quote }}
  host: {{ print $host | b64enc | quote }}
  port: {{ print $port | b64enc | quote }}
  username: {{ print .Values.auth.rootUser | b64enc | quote }}
  password: {{ print $rootPassword | b64enc | quote }}
  database: {{ print "admin" | b64enc | quote }}
  uri: {{ printf "mongodb://%s:%s@%s/admin" .Values.auth.rootUser $rootPassword $hostForURI | b64enc | quote }}
{{- range $e, $i := until (len $customUsersList) }}
---
{{- $currentSecret := printf "%s-svcbind-%d" (include "common.names.fullname" $) $i }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $currentSecret }}
  namespace: {{ $.Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $.Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if $.Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $.Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/mongodb
data:
  {{- $currentUser := index $customUsersList $i }}
  {{- $currentDatabase := last $customDatabasesList }}
  {{- if gt (len $customDatabasesList) $i }}
  {{- $currentDatabase = index $customDatabasesList $i }}
  {{- end }}
  {{- $currentProvidedPassword := index $customPasswordsList $i }}
  {{- $currentPassword := include "common.secrets.lookup" (dict "secret" $currentSecret "key" "password" "defaultValue" $currentProvidedPassword "context" $) | b64dec }}
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "mongodb" | b64enc | quote }}
  host: {{ print $host | b64enc | quote }}
  port: {{ print $port | b64enc | quote }}
  username: {{ print $currentUser | b64enc | quote }}
  password: {{ print $currentPassword | b64enc | quote }}
  database: {{ print $currentDatabase | b64enc | quote }}
  uri: {{ printf "mongodb://%s:%s@%s/%s" $currentUser $currentPassword $hostForURI $currentDatabase | b64enc | quote }}
{{- end }}
{{- end }}
{{- end }}
