annotations:
  category: DeveloperTools
  images: |
    - name: os-shell
      image: docker.io/bitnami/os-shell:12-debian-12-r26
    - name: parse
      image: docker.io/bitnami/parse:7.2.0-debian-12-r2
    - name: parse-dashboard
      image: docker.io/bitnami/parse-dashboard:5.4.0-debian-12-r4
  licenses: Apache-2.0
apiVersion: v2
appVersion: 7.2.0
dependencies:
- name: mongodb
  repository: oci://registry-1.docker.io/bitnamicharts
  version: 15.x.x
- name: common
  repository: oci://registry-1.docker.io/bitnamicharts
  tags:
  - bitnami-common
  version: 2.x.x
description: Parse is a platform that enables users to add a scalable and powerful
  backend to launch a full-featured app for iOS, Android, JavaScript, Windows, Unity,
  and more.
home: https://bitnami.com
icon: https://bitnami.com/assets/stacks/parse/img/parse-stack-220x234.png
keywords:
- parse
- backend
- serverless
- platform
- mbaas
- mobile
maintainers:
- name: Broadcom, Inc. All Rights Reserved.
  url: https://github.com/bitnami/charts
name: parse
sources:
- https://github.com/bitnami/charts/tree/main/bitnami/parse
version: 23.2.12
