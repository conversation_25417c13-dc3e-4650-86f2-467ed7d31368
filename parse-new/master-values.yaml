service:
  type: NodePort
  port: 80
ingress:
  hostdomain: services.wedeliverapp.com

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-parse-sa-role

database:
  host: productioncluster.grozi.mongodb.net

secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/mongo-database/parse_database_credentials
      environmentVariables:
        - environmentVariableName: PARSE_DATABASE_USER
          secretKey: username
        - environmentVariableName: PARSE_DATABASE_PASSWORD
          secretKey: password
    - asmName: production/parse/parse_dashboard_credentials
      environmentVariables:
        - environmentVariableName: PARSE_DASHBOARD_USER
          secretKey: username
        - environmentVariableName: PARSE_DASHBOARD_PASSWORD
          secretKey: password
    - asmName: production/parse/master_key
      environmentVariables:
        - environmentVariableName: PARSE_MASTER_KEY
          secretKey: SELF
    - asmName: production/parse/parse_secret_configurations
      environmentVariables:
        - environmentVariableName: PARSE_SERVER_JAVASCRIPT_KEY
          secretKey: javascriptKey
        - environmentVariableName: PARSE_SERVER_CLIENT_KEY
          secretKey: clientKey
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: parse
