{{- if .Values.secretProviderClass.create -}}
apiVersion: {{ .Values.secretProviderClass.version | default "secrets-store.csi.x-k8s.io/v1" }}
kind: SecretProviderClass
metadata:
  name: {{ include "common.names.fullname" . }}
  labels: {{ include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  provider: aws
  {{- if .Values.secretProviderClass.createEnvironmentVariables }}
  secretObjects:
  - secretName: {{ printf "%s-asm-secrets" .Values.secretProviderClass.name }}
    type: Opaque
    labels:
        {{- include "common.labels.standard" . | nindent 6 }}
    data:
    {{- range $secret := .Values.secretProviderClass.secrets }}
    {{- range $env := $secret.environmentVariables }}
    - objectName: {{ printf "%s%s" $secret.asmName ( ternary (printf ":%s" $env.secretKey) "" (ne ($env.secretKey) "SELF")) | replace "/" ":" }}
      key: {{ $env.environmentVariableName }}
    {{- end }}
    {{- end }}
  {{- end }}
  parameters:
    region: eu-west-1
    pathTranslation: ":"
    objects: |
      {{- range $secret := .Values.secretProviderClass.secrets }} 
      - objectName: "{{ $secret.asmName }}"
        objectType: secretsmanager
        {{- if $secret.environmentVariables }}
        jmesPath:
        {{- range $env := $secret.environmentVariables }}
        {{- if ne $env.secretKey "SELF" }}
            - path: "{{ $env.secretKey }}"
              objectAlias: "{{ printf "%s:%s" $secret.asmName $env.secretKey | replace "/" ":" }}"
        {{- end }}
        {{- end }}
        {{- end }}
      {{- end }}
{{- end }}
