{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.ingress.enabled }}
apiVersion: {{ template "common.capabilities.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if or .Values.ingress.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.ingress.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.ingressClassName (eq "true" (include "common.ingress.supportsIngressClassname" .)) }}
  ingressClassName: {{ .Values.ingress.ingressClassName | quote }}
  {{- end }}
  rules:
    {{- if .Values.dashboard.enabled }}
    {{- if .Values.ingress.dashboard.hostname }}
    - host: {{ .Values.ingress.dashboard.hostname }}
      http:
        paths:
          {{- if .Values.ingress.dashboard.extraPaths }}
          {{- toYaml .Values.ingress.dashboard.extraPaths | nindent 10 }}
          {{- end }}
          - path: {{ .Values.ingress.dashboard.path }}
            {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
            pathType: {{ .Values.ingress.dashboard.pathType }}
            {{- end }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (include "parse.dashboard.fullname" .) "servicePort" "http-dashboard" "context" $)  | nindent 14 }}
    {{- end }}
    {{- range .Values.ingress.dashboard.extraHosts }}
    - host: {{ .name | quote }}
      http:
        paths:
          - path: {{ default "/" .path }}
            {{- if eq "true" (include "common.ingress.supportsPathType" $) }}
            pathType: {{ default "ImplementationSpecific" .pathType }}
            {{- end }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (include "parse.dashboard.fullname" .) "servicePort" "http-dashboard" "context" $) | nindent 14 }}
    {{- end }}
  {{- end }}
  {{- if .Values.ingress.server.hostname }}
    - host: {{ .Values.ingress.server.hostname }}
      http:
        paths:
          {{- if .Values.ingress.server.extraPaths }}
          {{- toYaml .Values.ingress.server.extraPaths | nindent 10 }}
          {{- end }}
          - path: {{ .Values.ingress.server.path }}
            {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
            pathType: {{ .Values.ingress.server.pathType }}
            {{- end }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (include "common.names.fullname" .) "servicePort" "http-server" "context" $)  | nindent 14 }}
    {{- end }}
    {{- range .Values.ingress.server.extraHosts }}
    - host: {{ .name | quote }}
      http:
        paths:
          - path: {{ default "/" .path }}
            {{- if eq "true" (include "common.ingress.supportsPathType" $) }}
            pathType: {{ default "ImplementationSpecific" .pathType }}
            {{- end }}
            backend: {{- include "common.ingress.backend" (dict "serviceName" (include "common.names.fullname" $) "servicePort" "http-server" "context" $) | nindent 14 }}
    {{- end }}
    {{- if .Values.ingress.extraRules }}
    {{- include "common.tplvalues.render" (dict "value" .Values.ingress.extraRules "context" $) | nindent 4 }}
    {{- end }}
  {{- if or .Values.ingress.tls .Values.ingress.extraTls }}
  tls:
    {{- if .Values.ingress.tls }}
    {{- if .Values.dashboard.enabled }}
    - hosts:
        - {{ .Values.ingress.dashboard.hostname }}
      secretName: {{ printf "%s-tls" .Values.ingress.dashboard.hostname }}
    {{- end }}
    - hosts:
        - {{ .Values.ingress.server.hostname }}
      secretName: {{ printf "%s-tls" .Values.ingress.server.hostname }}
    {{- end }}
    {{- if .Values.ingress.extraTls }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.ingress.extraTls "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
