{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: dashboard
  {{- if or .Values.server.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.server.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.server.service.type }}
  {{- if and .Values.server.service.clusterIP (eq .Values.server.service.type "ClusterIP") }}
  clusterIP: {{ .Values.server.service.clusterIP }}
  {{- end }}
  {{- if .Values.server.service.sessionAffinity }}
  sessionAffinity: {{ .Values.server.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.server.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.server.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.server.service.type "LoadBalancer") (eq .Values.server.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.server.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.server.service.type "LoadBalancer") (not (empty .Values.server.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.server.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.server.service.type "LoadBalancer") (not (empty .Values.server.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.server.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http-server
      port: {{ .Values.server.service.ports.http }}
      targetPort: http-server
      {{- if (and (eq .Values.server.service.type "NodePort") (not (empty .Values.server.service.nodePorts.http))) }}
      nodePort: {{ .Values.server.service.nodePorts.http }}
      {{- end }}
    {{- if .Values.server.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.server.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.server.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: server
