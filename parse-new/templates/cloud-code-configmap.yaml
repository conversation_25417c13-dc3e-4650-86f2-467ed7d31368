{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.server.enableCloudCode (or .Values.server.cloudCodeScripts (.Files.Glob "files/cloud/*.js")) (not .Values.server.existingCloudCodeScriptsCM) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-cloud-code-scripts" include "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: server
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
{{- with .Files.Glob "files/cloud/*.js" }}
{{ .AsConfig | indent 2 }}
{{- end }}
{{- if .Values.server.cloudCodeScripts }}
{{- include "common.tplvalues.render" (dict "value" .Values.server.cloudCodeScripts "context" $) | nindent 2 }}
{{- end }}
{{- end }}
