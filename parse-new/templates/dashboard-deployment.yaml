{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and (include "parse.host" .) .Values.dashboard.enabled -}}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "parse.dashboard.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" ( include "common.images.version" ( dict "imageRoot" .Values.dashboard.image "chart" .Chart ) ) }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.commonLabels $versionLabel ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: dashboard
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.dashboard.replicaCount }}
  {{- if .Values.dashboard.updateStrategy }}
  strategy: {{- toYaml .Values.dashboard.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.dashboard.podLabels .Values.commonLabels $versionLabel ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: dashboard
  template:
    metadata:
      {{- if .Values.dashboard.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: dashboard
    spec:
      serviceAccountName: {{ template "parse.serviceAccountName" . }}
      {{- include "parse.imagePullSecrets" . | nindent 6 }}
      automountServiceAccountToken: {{ .Values.dashboard.automountServiceAccountToken }}
      {{- if .Values.dashboard.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.dashboard.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.dashboard.podAffinityPreset "component" "dashboard" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.dashboard.podAntiAffinityPreset "component" "dashboard" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.dashboard.nodeAffinityPreset.type "key" .Values.dashboard.nodeAffinityPreset.key "values" .Values.dashboard.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.dashboard.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.dashboard.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.priorityClassName }}
      priorityClassName: {{ .Values.dashboard.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.dashboard.schedulerName }}
      schedulerName: {{ .Values.dashboard.schedulerName | quote }}
      {{- end }}
      {{- if .Values.dashboard.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.podSecurityContext.enabled }}
      securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.dashboard.podSecurityContext "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.dashboard.terminationGracePeriodSeconds }}
      {{- end }}
      initContainers:
        - name: create-default-config
          image: {{ include "parse.dashboard.image" . }}
          imagePullPolicy: {{ .Values.dashboard.image.pullPolicy | quote }}
          {{- if .Values.dashboard.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.dashboard.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          command:
            - /bin/bash
          args:
            - -ec
            - |
              #!/bin/bash

              # HACK: We need to create this file in the emptyDir volume to make it writable
              # in the
              touch /default-conf/config.json
          {{- if .Values.dashboard.resources }}
          resources: {{- toYaml .Values.dashboard.resources | nindent 12 }}
          {{- else if ne .Values.dashboard.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.dashboard.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /default-conf
              subPath: app-default-conf-dir
        {{- if .Values.dashboard.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: parse-dashboard
          image: {{ include "parse.dashboard.image" . }}
          imagePullPolicy: {{ .Values.dashboard.image.pullPolicy | quote }}
          {{- if .Values.dashboard.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.dashboard.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.dashboard.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.dashboard.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.dashboard.image.debug .Values.diagnosticMode.enabled) | quote }}
            - name: PARSE_DASHBOARD_USER
              value: {{ .Values.dashboard.username }}
            - name: PARSE_DASHBOARD_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "common.names.fullname" . }}
                  key: parse-dashboard-password
            - name: PARSE_HOST
              value: {{ include "parse.host" . | quote }}
            - name: PARSE_USE_HOSTNAME
              value: {{ ternary "yes" "no" .Values.ingress.enabled | quote }}
            - name: PARSE_PORT_NUMBER
              value: {{ include "parse.external-port" . | quote }}
            - name: PARSE_PROTOCOL
              value: {{ .Values.dashboard.parseServerUrlProtocol | quote }}
            - name: PARSE_APP_ID
              value: {{ .Values.server.appId | quote }}
            - name: PARSE_MASTER_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "common.names.fullname" . }}
                  key: master-key
            - name: PARSE_DASHBOARD_APP_NAME
              value: {{ .Values.dashboard.appName | quote }}
            - name: PARSE_FORCE_OVERWRITE_CONF_FILE
              value: {{ ternary "yes" "no" .Values.server.forceOverwriteConfFile | quote }}
            {{- if .Values.dashboard.extraEnvVars }}
              {{- include "common.tplvalues.render" ( dict "value" .Values.dashboard.extraEnvVars "context" $ ) | nindent 12 }}
            {{- end }}
          {{- if or .Values.dashboard.extraEnvVarsCM .Values.dashboard.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.dashboard.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" ( dict "value" .Values.dashboard.extraEnvVarsCM "context" $ ) }}
            {{- end }}
            {{- if .Values.dashboard.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" ( dict "value" .Values.dashboard.extraEnvVarsSecret "context" $ ) }}
            {{- end }}
          {{- end }}
          {{- if .Values.dashboard.resources }}
          resources: {{- toYaml .Values.dashboard.resources | nindent 12 }}
          {{- else if ne .Values.dashboard.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.dashboard.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http-dashboard
              containerPort: {{ .Values.dashboard.containerPorts.http }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.dashboard.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.dashboard.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.dashboard.livenessProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: http-dashboard
          {{- end }}
          {{- if .Values.dashboard.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.dashboard.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.dashboard.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /
              port: http-dashboard
          {{- end }}
          {{- if .Values.dashboard.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.dashboard.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.dashboard.startupProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: http-dashboard
          {{- end }}
          {{- end }}
          {{- if .Values.dashboard.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.dashboard.resources }}
          resources: {{- toYaml .Values.dashboard.resources | nindent 12 }}
          {{- else if ne .Values.dashboard.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.dashboard.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /bitnami/parse-dashboard
              subPath: app-data-dir
            - name: empty-dir
              mountPath: /opt/bitnami/parse-dashboard/config.json
              subPath: app-default-conf-dir/config.json
            - name: empty-dir
              mountPath: /opt/bitnami/parse-dashboard/tmp
              subPath: app-tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/parse-dashboard/logs
              subPath: app-logs-dir
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
          {{- if .Values.dashboard.extraVolumeMounts }}
          {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.extraVolumeMounts "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.dashboard.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.dashboard.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        - name: empty-dir
          emptyDir: {}
        {{- if .Values.dashboard.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
{{- end -}}
