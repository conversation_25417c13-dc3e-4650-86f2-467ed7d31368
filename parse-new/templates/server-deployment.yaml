{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "parse.server.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: server
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.server.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: server
  replicas: 1
  {{- if .Values.server.updateStrategy }}
  strategy: {{- toYaml .Values.server.updateStrategy | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: server
      {{- if .Values.server.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.server.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
    spec:
      {{- if .Values.server.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.server.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.server.podAffinityPreset "component" "server" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.server.podAntiAffinityPreset "component" "server" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.server.nodeAffinityPreset.type "key" .Values.server.nodeAffinityPreset.key "values" .Values.server.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.server.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.server.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ template "parse.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.server.automountServiceAccountToken }}
      {{- if .Values.server.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.server.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.server.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.priorityClassName }}
      priorityClassName: {{ .Values.server.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.server.schedulerName }}
      schedulerName: {{ .Values.server.schedulerName | quote }}
      {{- end }}
      {{- if .Values.server.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.server.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.podSecurityContext.enabled }}
      securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.server.podSecurityContext "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.server.terminationGracePeriodSeconds }}
      {{- end }}
      {{- include "parse.imagePullSecrets" . | nindent 6 }}
      initContainers:
        {{- if and .Values.volumePermissions.enabled .Values.persistence.enabled }}
        - name: volume-permissions
          image: {{ include "parse.volumePermissions.image" . }}
          imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
          command:
            - /bin/bash
          args:
            - -ec
            - |
              mkdir -p /bitnami/parse
              find /bitnami/parse -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | xargs -r chown -R {{ .Values.server.containerSecurityContext.runAsUser }}:{{ .Values.server.podSecurityContext.fsGroup }}
          {{- if .Values.server.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.server.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          resources: {{ toYaml .Values.volumePermissions.resources | nindent 12 }}
          volumeMounts:
            - name: parse-data
              mountPath: /bitnami/parse
        {{- end }}
        - name: create-default-config
          image: {{ include "parse.server.image" . }}
          imagePullPolicy: {{ .Values.server.image.pullPolicy | quote }}
          {{- if .Values.server.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.server.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          command:
            - /bin/bash
          args:
            - -ec
            - |
              #!/bin/bash

              # HACK: We need to create this file in the emptyDir volume to make it writable
              # in the
              touch /default-conf/config.json
          {{- if .Values.server.resources }}
          resources: {{- toYaml .Values.server.resources | nindent 12 }}
          {{- else if ne .Values.server.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.server.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /default-conf
              subPath: app-default-conf-dir
        {{- if .Values.server.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.server.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: parse
          image: {{ include "parse.server.image" . }}
          imagePullPolicy: {{ .Values.server.image.pullPolicy | quote }}
          {{- if .Values.server.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.server.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.server.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.server.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.server.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.server.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.server.image.debug .Values.diagnosticMode.enabled) | quote }}
            - name: PARSE_HOST
              value: {{ include "parse.host" . | quote }}
            - name: PARSE_SERVER_MASTER_KEY_IPS
              value: {{ .Values.server.masterKeyIps | quote }}
            - name: PARSE_PORT_NUMBER
              value: {{ .Values.server.containerPorts.http | quote }}
            - name: PARSE_MOUNT_PATH
              value: {{ .Values.server.mountPath | quote }}
            - name: PARSE_APP_ID
              value: {{ .Values.server.appId | quote }}
            - name: PARSE_MASTER_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "common.names.fullname" . }}
                  key: master-key
            - name: PARSE_ENABLE_CLOUD_CODE
              value: {{ ternary "yes" "no" .Values.server.enableCloudCode | quote }}
            - name: PARSE_DATABASE_HOST
              value: {{ .Values.database.host | quote }}
            - name: PARSE_DATABASE_NAME
              value: {{ .Values.database.name | quote }}
            - name: PARSE_DATABASE_PORT_NUMBER
              value: {{ .Values.database.port | quote }}
            {{- if .Values.mongodb.auth.enabled }}
            - name: PARSE_DATABASE_USER
              value: {{ .Values.mongodb.auth.username | quote }}
            - name: PARSE_DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "parse.mongodb.fullname" . }}
                  key: mongodb-passwords
            {{- end }}
            - name: PARSE_FORCE_OVERWRITE_CONF_FILE
              value: {{ ternary "yes" "no" .Values.server.forceOverwriteConfFile | quote }}
            {{- if .Values.server.extraEnvVars }}
              {{- include "common.tplvalues.render" ( dict "value" .Values.server.extraEnvVars "context" $ ) | nindent 12 }}
            {{- end }}
          {{- if or .Values.server.extraEnvVarsCM .Values.server.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.server.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" ( dict "value" .Values.server.extraEnvVarsCM "context" $ ) }}
            {{- end }}
            {{- if .Values.server.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" ( dict "value" .Values.server.extraEnvVarsSecret "context" $ ) }}
            {{- end }}
          {{- end }}
          ports:
            - name: http-server
              containerPort: {{ .Values.server.containerPorts.http }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.server.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.server.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.server.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.server.livenessProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: http-server
          {{- end }}
          {{- if .Values.server.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.server.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.server.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.server.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: {{ .Values.server.mountPath }}/users
              port: http-server
              httpHeaders:
                - name: X-Parse-Application-Id
                  value: {{ .Values.server.appId }}
          {{- end }}
          {{- if .Values.server.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.server.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.server.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.server.startupProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: http-server
          {{- end }}
          {{- end }}
          {{- if .Values.server.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.server.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.server.resources }}
          resources: {{- toYaml .Values.server.resources | nindent 12 }}
          {{- else if ne .Values.server.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.server.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: parse-data
              mountPath: /bitnami/parse
            - name: empty-dir
              mountPath: /opt/bitnami/parse/config.json
              subPath: app-default-conf-dir/config.json
            - name: empty-dir
              mountPath: /opt/bitnami/parse/tmp
              subPath: app-tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/parse/logs
              subPath: app-logs-dir
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            {{- if and .Values.server.enableCloudCode (or (.Files.Glob "files/cloud/*.js") .Values.server.cloudCodeScripts .Values.server.existingCloudCodeScriptsCM) }}
            - name: cloud-code-config
              mountPath: /opt/bitnami/parse/cloud
            {{- end }}
          {{- if .Values.server.extraVolumeMounts }}
          {{- include "common.tplvalues.render" (dict "value" .Values.server.extraVolumeMounts "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.server.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.server.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        - name: empty-dir
          emptyDir: {}
        {{- if and .Values.server.enableCloudCode (or (.Files.Glob "files/cloud/*.js") .Values.server.cloudCodeScripts .Values.server.existingCloudCodeScriptsCM) }}
        - name: cloud-code-config
          configMap:
            name: {{ include "parse.cloudCodeScriptsCMName" . }}
        {{- end }}
        - name: parse-data
        {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ default (include "common.names.fullname" .) .Values.persistence.existingClaim }}
        {{- else }}
          emptyDir: {}
        {{- end }}
        {{- if .Values.server.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.server.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
