{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.server.networkPolicy.enabled }}
kind: NetworkPolicy
apiVersion: {{ include "common.capabilities.networkPolicy.apiVersion" . }}
metadata:
  name: {{ template "parse.server.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: server
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.server.podLabels .Values.commonLabels ) "context" . ) }}
  podSelector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: server
  policyTypes:
    - Ingress
    - Egress
  {{- if .Values.server.networkPolicy.allowExternalEgress }}
  egress:
    - {}
  {{- else }}
  egress:
    # Allow dns resolution
    - ports:
        - port: 53
          protocol: UDP
        - port: 53
          protocol: TCP
    # Allow access to mongodb
    - ports:
        {{/* TODO: Allow changing the mongodb port in the chart YAML */}}
        - port: 27017
      to: 
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: mongodb
              app.kubernetes.io/instance: {{ .Release.Name }}
    {{- if .Values.server.networkPolicy.extraEgress }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.server.networkPolicy.extraEgress "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
  ingress:
    - ports:
        - port: {{ .Values.server.containerPorts.http }}
      {{- if not .Values.server.networkPolicy.allowExternal }}
      from:
        - podSelector:
            matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 14 }}
        - podSelector:
            matchLabels:
              {{ template "parse.server.fullname" . }}-client: "true"
        {{- if .Values.server.networkPolicy.ingressNSMatchLabels }}
        - namespaceSelector:
            matchLabels:
              {{- range $key, $value := .Values.server.networkPolicy.ingressNSMatchLabels }}
              {{ $key | quote }}: {{ $value | quote }}
              {{- end }}
          {{- if .Values.server.networkPolicy.ingressNSPodMatchLabels }}
          podSelector:
            matchLabels:
              {{- range $key, $value := .Values.server.networkPolicy.ingressNSPodMatchLabels }}
              {{ $key | quote }}: {{ $value | quote }}
              {{- end }}
          {{- end }}
        {{- end }}
      {{- end }}
    {{- if .Values.server.networkPolicy.extraIngress }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.server.networkPolicy.extraIngress "context" $ ) | nindent 4 }}
    {{- end }}
{{- end }}
