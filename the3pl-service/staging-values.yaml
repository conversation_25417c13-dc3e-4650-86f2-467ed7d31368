envName: staging
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_3pl_service:7e6484b1524fe7d6e998a1f2289773333b714711
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/staging-the3pl-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: 3pl.services.wedeliver-staging.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: staging/master-database/the3pl_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: staging/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: staging/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: staging/salla/salla_credentials
      environmentVariables:
        - environmentVariableName: SALLA_WEBHOOK_SECRET_KEY
          secretKey: webhook_secret_key
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: the3pl-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: ksa_distributer_staging
  - name: FLASK_ENV
    value: staging
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: FINANCE_SERVICE
    value: http://finance-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: SUPPLIER_SERVICE
    value: http://supplier-service.services
  - name: PN_SERVICE
    value: http://pn-service.services
  - name: ADDRESS_SERVICE
    value: http://address-service.services
  - name: MLCGO_DELIVERY_ID
    value: '521'
  - name: SAFEARRIVAL_DELIVERY_ID
    value: '522'
  - name: ESNAD_DELIVERY_ID
    value: '400'
  - name: ARAMEX_DELIVERY_ID
    value: '500'
jobs:
  refresh-token-salla-stores:
    suspend: true
  wd-counter:
    schedule: '*/5 * * * *'
    suspend: true
