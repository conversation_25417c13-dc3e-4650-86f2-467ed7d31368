apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "the3pl-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "the3pl-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "the3pl-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
