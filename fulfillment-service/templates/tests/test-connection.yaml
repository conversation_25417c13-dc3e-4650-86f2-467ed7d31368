apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "fulfillment-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "fulfillment-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "fulfillment-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
