apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "mail-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "mail-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "mail-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
