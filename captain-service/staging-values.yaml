envName: staging
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_captain_service:d95b6bfcee925b74b7d1247e5e1bed6072049286
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/staging-captain-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: captain.services.wedeliver-staging.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: staging/master-database/captain_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: staging/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: staging/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: staging/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: staging/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: staging/parse/parse_secret_configurations
      environmentVariables:
        - environmentVariableName: PARSE_SERVER_REST_API_KEY
          secretKey: restAPIKey
    - asmName: staging/patch/patch_credential
      environmentVariables:
        - environmentVariableName: PATCH_USER_NAME
          secretKey: username
        - environmentVariableName: PATCH_PASSWORD
          secretKey: password
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: captain-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: captaindb
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: FLASK_ENV
    value: staging
  - name: FLASK_APP
    value: app.py
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: USE_IN_SERVER_IMAGES
    value: 'False'
  - name: PROJECT_BASE_URL
    value: https://captain.service.wedeliver-staging.com
  - name: UPLOAD_FOLDER
    value: static/images
  - name: PROJECT_IMAGES_PATH
    value: ''
  - name: WEDELIVER_SUPPLIER_PROFILE_ID
    value: ''
  - name: DEFFAULT_TEAM_ID
    value: ''
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'FALSE'
  - name: S3_REGION
    value: eu-west-1
  - name: S3_BUCKET
    value: captainportalattachments
  - name: FINANCE_SERVICE
    value: http://finance-service.services
  - name: SUPPLIER_SERVICE
    value: http://supplier-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: PN_SERVICE
    value: http://pn-service.services
  - name: PARSE_BASE_URL
    value: https://parse-server.services.wedeliver-staging.com/parse
  - name: PARSE_APP_ID
    value: wedeliver-tracker
  - name: STC_SERVICE
    value: http://stc-service.services
  - name: FRONT_END_WEBSITE_URL
    value: https://business.wedeliver-staging.com
  - name: FINTECH_SERVICE
    value: http://fintech-service.services
jobs:
  auto-sign-captains-to-shifts:
    suspend: true
  close-shifts-finacially:
    suspend: true
  delete-old-location-history:
    suspend: true
  offline-out-of-gps-captains:
    suspend: true
    schedule: '*/5 * * * *'
  send-shifts-reminders:
    suspend: true
  alert-expired-docs-dates:
    suspend: true
  unutilized-captains-report:
    suspend: true
  daily-report-tgp:
    suspend: true
  monthly-report-tgp:
    suspend: true
  weekly-report-tgp:
    suspend: true
