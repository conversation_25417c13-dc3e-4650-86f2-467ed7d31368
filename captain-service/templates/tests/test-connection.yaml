apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "captain-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "captain-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "captain-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
