team: backend-team
nameOverride: ''
fullnameOverride: ''
replicaCount: 1
revisionHistoryLimit: 10
maxUnavailable: 25%
maxSurge: 25%
progressDeadlineSeconds: 60
terminationGracePeriodSeconds: 30
image:
  repository: 026392744560.dkr.ecr.eu-west-1.amazonaws.com/backend_captain_service
  pullPolicy: IfNotPresent
  tag: 626faed65f80522ad8798264ee70e8e6eda3f6b7
imagePullSecrets: []
enableDBMigration: false
containerPorts:
  - containerPort: 8000
    name: http
    protocol: TCP
livenessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 30
  timeoutSeconds: 15
  periodSeconds: 15
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 30
  timeoutSeconds: 15
  periodSeconds: 15
  successThreshold: 1
  failureThreshold: 3
commonEnvironmentVariables:
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: SERVICE_NAME
    value: captain
  - name: DATABASE_SERVER
    value: master-database.default
  - name: DATABASE_READONLY
    value: read-database-replica.default
  - name: QUICKBOOKS_QBO_BASEURL
    value: https://quickbook-server.default
  - name: COUNTRY_CODE
    value: sa
volumeMounts: []
volumes: []
serviceAccount:
  create: false
  annotations: {}
  name: ''
secretProviderClass:
  version: secrets-store.csi.x-k8s.io/v1
  create: false
  annotations: {}
  name: ''
  secrets: []
podAnnotations: {}
podSecurityContext: {}
securityContext: {}
service:
  type: ClusterIP
  port: 80
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: worker_group=services
resources:
  limits:
    cpu: 300m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
pdb:
  enabled: false
  minAvailable: 1
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  version: autoscaling/v2
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
  behavior:
    scaledown:
      stabilizationWindowSeconds: 180
      policies:
        - type: Pods
          value: 1
          periodSeconds: 60
    scaleup:
      stabilizationWindowSeconds: 0
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 2
          periodSeconds: 15
      selectPolicy: Max
nodeSelector:
  worker_group: services
tolerations: []
affinity: {}
jobs:
  auto-sign-captains-to-shifts:
    successfulJobsHistoryLimit: 0
    suspend: true
    schedule: '2-47/15 * * * *'
    args:
      - '-c'
      - python auto_sign_in_and_out_captains_to_shifts.py
  close-shifts-finacially:
    suspend: true
    schedule: 4 * * * *
    args:
      - '-c'
      - python close_shifts_finacially.py
  delete-old-location-history:
    suspend: true
    schedule: 5 2 * * *
    args:
      - '-c'
      - python delete_old_location_history.py
  offline-out-of-gps-captains:
    successfulJobsHistoryLimit: 0
    suspend: true
    schedule: '*/2 * * * *'
    args:
      - '-c'
      - python make_out_of_gps_sync_captains_offline.py
  send-shifts-reminders:
    successfulJobsHistoryLimit: 0
    suspend: true
    schedule: '10-55/5 * * * *'
    args:
      - '-c'
      - python send_shifts_reminders.py
  alert-expired-docs-dates:
    suspend: true
    schedule: 0 23 * * *
    args:
      - '-c'
      - python cronjob_alert_expired_docs_dates.py
  unutilized-captains-report:
    suspend: true
    schedule: 0 5 * * *
    args:
      - '-c'
      - python daily_report_unutilized_captains.py
  daily-report-tgp:
    suspend: true
    schedule: 10 23 * * *
    args:
      - '-c'
      - python tgp_daily_report_history_cron_job.py
  monthly-report-tgp:
    suspend: true
    schedule: 15 23 * * *
    args:
      - '-c'
      - python tgp_monthly_report_history_cron_job.py
  weekly-report-tgp:
    suspend: true
    schedule: 5 0 * * SUN
    args:
      - '-c'
      - python tgp_weekly_report_history_cron_job.py
