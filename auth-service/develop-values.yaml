envName: development
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_auth_service:12ce0b7b9befb4318e45f6440db0e6a8ed0182ff
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-auth-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: auth.services.wedeliver-dev.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: develop/master-database/auth_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: develop/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: develop/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: develop/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: develop/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: develop/parse/parse_secret_configurations
      environmentVariables:
        - environmentVariableName: PARSE_SERVER_REST_API_KEY
          secretKey: restAPIKey
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: auth-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: auth_component
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'False'
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: FLASK_ENV
    value: development
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: COUNTRY
    value: KSA
  - name: CURRENCY
    value: SAR
  - name: CSRF_ENABLED
    value: 'True'
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SMS_SERVICE
    value: http://sms-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: S3_REGION
    value: eu-west-1
  - name: S3_BUCKET
    value: dev-wedeliver-slips
  - name: AMAZON_SLIP_URL
    value: https://dev-wedeliver-slips.s3-eu-west-1.amazonaws.com/
  - name: WEBSITE_URL
    value: https://business.wedeliver-dev.com/
  - name: PARSE_BASE_URL
    value: https://parse-server.services.wedeliver-dev.com/parse
  - name: PARSE_APP_ID
    value: wedeliver-tracker
