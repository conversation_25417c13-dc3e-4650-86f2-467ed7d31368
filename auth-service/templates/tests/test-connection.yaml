apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "auth-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "auth-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "auth-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
