team: data-team

nameOverride: ""
fullnameOverride: ""

image:
  pullPolicy: IfNotPresent

commonEnvironmentVariables:
  - name: SERVICE_NAME
    value: fivetran-scheduler

serviceAccount:
  create: false
  annotations: {}
  name: ""

secretProviderClass:
  version: secrets-store.csi.x-k8s.io/v1
  create: false
  annotations: {}
  name: ""
  secrets: []

podAnnotations: {}
podSecurityContext: {}
securityContext: {}

resources:
  limits:
    cpu: 300m
    memory: 512Mi
  requests:
    cpu: 50m
    memory: 64Mi

nodeSelector:
  worker_group: services

tolerations: []
affinity: {}

jobs:
  sync-during-work:
    successfulJobsHistoryLimit: 0
    suspend: true
    # From 5 through 17:30, every 30 mins
    schedule: 0/30 5-17 * * *
  sync-after-work1:
    successfulJobsHistoryLimit: 0
    suspend: true
    # At 21 UTC, once.
    schedule: 0 21/4 * * *
  sync-after-work2:
    successfulJobsHistoryLimit: 0
    suspend: true
    # At 01 UTC, once.
    schedule: 0 1-4/4 * * *
