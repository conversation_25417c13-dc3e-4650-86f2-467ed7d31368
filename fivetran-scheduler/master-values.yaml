envName: production
image:
  repository: ************.dkr.ecr.eu-west-1.amazonaws.com/data_fivetran_scheduler
  tag: eee8da4d494d6e9e7dff24d6a2c8839469c73bec
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-fivetran-scheduler-sa-role
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/fivetran/fivetran_credentials
      environmentVariables:
        - environmentVariableName: FIVETRAN_API_KEY
          secretKey: fivetran_api_key
        - environmentVariableName: FIVETRAN_API_SECRET
          secretKey: fivetran_api_secret
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: fivetran-scheduler
environmentVariables:
  - name: FIVETRAN_CONNECTOR_ID
    value: brown_insofar
  - name: DEBUG
    value: 'false'
jobs:
  sync-during-work:
    suspend: true
  sync-after-work1:
    suspend: true
  sync-after-work2:
    suspend: true
