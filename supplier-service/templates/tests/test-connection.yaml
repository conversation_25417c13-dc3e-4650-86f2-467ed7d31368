apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "supplier-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "supplier-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "supplier-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
