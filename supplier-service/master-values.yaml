envName: production
pdb:
  enabled: true
autoscaling:
  maxReplicas: 5
  minReplicas: 2
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-supplier-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: supplier.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
    - host: api.wedeliverapp.com
      paths:
        - path: /sa/supplier/
          pathType: Prefix
        - path: /supplier/
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/master-database/supplier_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: production/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: production/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: supplier-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: supplierServiceDB
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'False'
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: FLASK_ENV
    value: production
  - name: FLASK_APP
    value: app.py
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: COUNTRY
    value: KSA
  - name: CURRENCY
    value: SAR
  - name: CSRF_ENABLED
    value: 'True'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: INVOICE_FRONT_END_URL
    value: https://public.wedeliverapp.com/invoice/
  - name: FINANCE_SERVICE
    value: http://finance-service.services
