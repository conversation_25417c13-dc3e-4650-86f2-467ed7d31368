envName: production
pdb:
  enabled: true
autoscaling:
  maxReplicas: 5
  minReplicas: 2
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-apilayer-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: apilayer.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
    - host: apis.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: production/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: apilayer-service
environmentVariables:
  - name: FLASK_ENV
    value: production
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: FINANCE_SERVICE
    value: http://finance-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: SUPPLIER_SERVICE
    value: http://supplier-service.services
  - name: PN_SERVICE
    value: http://pn-service.services
  - name: ADDRESS_SERVICE
    value: http://address-service.services
image:
  tag: 1656617268387b63595ce46d1c43cbda6dea6d47
