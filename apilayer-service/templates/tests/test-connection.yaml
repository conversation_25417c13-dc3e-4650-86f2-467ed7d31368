apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "apilayer-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "apilayer-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "apilayer-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
