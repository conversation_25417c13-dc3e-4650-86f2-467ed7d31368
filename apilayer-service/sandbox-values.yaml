envName: sandbox
autoscaling:
  maxReplicas: 2
  minReplicas: 1
enableDBMigration: false
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-apilayer-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: sandbox-ingress
    alb.ingress.kubernetes.io/target-node-labels: worker_group=sandbox
  hosts:
    - host: apilayer.services.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
    - host: apis.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
commonEnvironmentVariables:
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: SERVICE_NAME
    value: apilayer
  - name: QUICKBOOKS_QBO_BASEURL
    value: https://quickbook-server.sandbox
  - name: COUNTRY_CODE
    value: sa
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: sandbox/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: sandbox/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: apilayer-service-sandbox
environmentVariables:
  - name: FLASK_ENV
    value: sandbox
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SDD_SERVICE
    value: http://sdd-service-sandbox.sandbox
  - name: AUTH_SERVICE
    value: http://auth-service-sandbox.sandbox
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service-sandbox.sandbox
  - name: FINANCE_SERVICE
    value: http://finance-service-sandbox.sandbox
  - name: CAPTAIN_SERVICE
    value: http://captain-service-sandbox.sandbox
  - name: SUPPLIER_SERVICE
    value: http://supplier-service-sandbox.sandbox
  - name: PN_SERVICE
    value: http://pn-service-sandbox.sandbox
  - name: ADDRESS_SERVICE
    value: http://address-service-sandbox.sandbox
nodeSelector:
  worker_group: sandbox
image:
  tag: 1656617268387b63595ce46d1c43cbda6dea6d47
