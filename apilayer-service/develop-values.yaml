envName: development
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_apilayer_service:a69f422c17491e3b110495e1e4f9facc07aa26d6
autoscaling:
  maxReplicas: 3
enableDBMigration: false
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-apilayer-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: apilayer.services.wedeliver-dev.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: develop/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: develop/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: apilayer-service
environmentVariables:
  - name: FLASK_ENV
    value: development
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: FINANCE_SERVICE
    value: http://finance-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: SUPPLIER_SERVICE
    value: http://supplier-service.services
  - name: PN_SERVICE
    value: http://pn-service.services
  - name: ADDRESS_SERVICE
    value: http://address-service.services
