{{- $fullname := include "public-service.fullname" . }}
{{- $serviceAccountName := include "public-service.serviceAccountName" . }}
{{- $volumeMounts := include "public-service.volumeMounts" .}}
{{- $volumes := include "public-service.volumes" .}}
{{- $envs := include "public-service.envs" . }}

{{- if .Values.enableDBMigration }}
---
apiVersion: batch/v1
kind: Job
metadata:
  generateName: "{{ $fullname }}-db-"
  annotations:
    argocd.argoproj.io/hook: PreSync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
spec:
  backoffLimit: 0
  activeDeadlineSeconds: 100
  template:
    spec:
        serviceAccountName: {{ $serviceAccountName }}
        securityContext:
        {{- toYaml $.Values.podSecurityContext | nindent 12 }}
        containers:
          - image: {{ include "public-service.imageSource" . | indent 8 }}
            imagePullPolicy: {{ $.Values.image.pullPolicy }}
            name: "{{ $fullname }}-db-migrate"
            env:
                {{- $envs | nindent 12 }}
            command: 
                - "/bin/sh"
            args:
                - "-c"
                - "python plt_script_migrate-staging-develop-db.py"
                # - "echo \"init...\n\" && python3 manage.py db init"
                # - "&& echo \"migrate...\n\" && python3 manage.py db migrate"
                # - "&& echo \"upgarde...\n\" && python3 manage.py db upgrade"
            resources:
                {{- toYaml $.Values.resources | nindent 14 }}
            volumeMounts:
                {{- $volumeMounts | nindent 12 }}
        {{- with $.Values.nodeSelector }}
        nodeSelector:
        {{- toYaml . | nindent 12 }}
        {{- end }}
        {{- with $.Values.affinity }}
        affinity:
        {{- toYaml . | nindent 12 }}
        {{- end }}
        {{- with $.Values.tolerations }}
        tolerations:
        {{- toYaml . | nindent 12 }}
        {{- end }}
        volumes:
        {{- $volumes | nindent 12 }}
        restartPolicy: Never
{{- end }}
