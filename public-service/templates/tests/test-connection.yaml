apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "public-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "public-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "public-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
