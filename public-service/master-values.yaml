envName: production
pdb:
  enabled: true
autoscaling:
  maxReplicas: 5
  minReplicas: 2
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-public-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: public.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
    - host: api.wedeliverapp.com
      paths:
        - path: /sa/public/
          pathType: Prefix
        - path: /public/
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/master-database/public_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: production/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: production/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: production/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: production/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: public-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: publicServiceDB
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: FLASK_ENV
    value: production
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: COUNTRY
    value: KSA
  - name: CURRENCY
    value: SAR
  - name: CSRF_ENABLED
    value: 'True'
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: INVOICE_FRONT_END_URL
    value: https://business.wedeliverapp.com/public/invoice/
  - name: S3_REGION
    value: eu-west-1
  - name: S3_BUCKET
    value: wedeliver-backend-assets
  - name: AMAZON_SLIP_URL
    value: https://wedeliver-backend-assets.s3-eu-west-1.amazonaws.com/
