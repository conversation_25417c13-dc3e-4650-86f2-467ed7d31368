envName: sandbox
autoscaling:
  maxReplicas: 2
  minReplicas: 1
nameOverride: ''
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-public-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: sandbox-ingress
    alb.ingress.kubernetes.io/target-node-labels: worker_group=sandbox
  hosts:
    - host: public.services.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
commonEnvironmentVariables:
  - name: SERVICE_NAME
    value: public
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: DATABASE_SERVER
    value: master-database.sandbox
  - name: DATABASE_READONLY
    value: read-database-replica.sandbox
  - name: QUIC<PERSON>BOOKS_QBO_BASEURL
    value: https://quickbook-server.sandbox
  - name: COUNTRY_CODE
    value: sa
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: sandbox/master-database/public_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: sandbox/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: sandbox/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: sandbox/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: sandbox/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: public-service-sandbox
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: publicDB
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: FLASK_ENV
    value: sandbox
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: COUNTRY
    value: KSA
  - name: CURRENCY
    value: SAR
  - name: CSRF_ENABLED
    value: 'True'
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: SDD_SERVICE
    value: http://sdd-service-sandbox.sandbox
  - name: AUTH_SERVICE
    value: http://auth-service-sandbox.sandbox
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service-sandbox.sandbox
  - name: INVOICE_FRONT_END_URL
    value: https://business.wedeliverapp.dev/public/invoice/
  - name: S3_REGION
    value: eu-west-1
  - name: S3_BUCKET
    value: wedeliver-slips
  - name: AMAZON_SLIP_URL
    value: https://wedeliver-slips.s3-eu-west-1.amazonaws.com/
nodeSelector:
  worker_group: sandbox
