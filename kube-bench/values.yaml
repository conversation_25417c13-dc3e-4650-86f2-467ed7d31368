team: platform-team

podAnnotations: {}
podSecurityContext: {}
securityContext: {}

image:
  repository: docker.io/aquasec/kube-bench
  tag: latest

serviceAccount:
  create: true

config:
  create: true

nodeSelector:
  worker_group: general

jobs:
  # This job send findings to AWS SecurityHub
  asff:
    successfulJobsHistoryLimit: 1
    failedJobsHistoryLimit: 1
    suspend: true
    # At 5 UTC, once at the start of the month.
    schedule: 0 5 1 * *
  # This job create a report locally
  normal:
    successfulJobsHistoryLimit: 1
    failedJobsHistoryLimit: 1
    suspend: true
    # At 5 UTC, once at the start of the month.
    schedule: 0 5 1 * *

# AWS SecurityHub Audit Account Details
audit:
  account: "************"
  region: eu-west-1

target:
  account: ""
  region: eu-west-1
  cluster: wedeliver-services
  name: ""
