{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "kube-bench.serviceAccountName" . }}
  labels:
    {{- include "kube-bench.labels" . | nindent 4 }}
  annotations:
    eks.amazonaws.com/role-arn: "{{- printf "arn:aws:iam::%s:role/security-audit-%s-kube-bench-sa-role" .Values.audit.account .Values.target.name -}}"
    {{- with .Values.serviceAccount.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
