{{- $fullname := include "kube-bench.fullname" . }}
{{- $labels := include "kube-bench.labels" . }}
{{- $serviceAccountName := include "kube-bench.serviceAccountName" . }}
{{- $imageSource := include "kube-bench.imageSource" . }}

{{- range $key, $job := .Values.jobs }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: "{{ $fullname }}-{{ $key }}"
  labels:
    {{- $labels | nindent 4 }}
    cron: "{{ $fullname }}-{{ $key }}"
spec:
  suspend: {{ $job.suspend }}
  schedule: {{ $job.schedule | quote }}
  concurrencyPolicy: {{ default "Forbid" $job.concurrencyPolicy }}
  failedJobsHistoryLimit: {{ if kindIs "float64" $job.failedJobsHistoryLimit }}{{ $job.failedJobsHistoryLimit }}{{ else }}{{ 3 }}{{ end }}
  successfulJobsHistoryLimit: {{ if kindIs "float64" $job.successfulJobsHistoryLimit }}{{ $job.successfulJobsHistoryLimit }}{{ else }}{{ 1 }}{{ end }}
  jobTemplate:
    spec:
      backoffLimit: {{ default "2" $job.backoffLimit  }}
      activeDeadlineSeconds: {{ default "100" $job.activeDeadlineSeconds }}
      ttlSecondsAfterFinished: {{ default "300" $job.ttlSecondsAfterFinished }}
      template:
        metadata:
          {{- with $.Values.podAnnotations }}
          annotations:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          labels:
            {{- $labels | nindent 12 }}
            job: "{{ $fullname }}-{{ $key }}"
        spec:
          serviceAccountName: {{ $serviceAccountName }}
          securityContext:
            {{- toYaml $.Values.podSecurityContext | nindent 12 }}
          containers:
          - image: {{ $imageSource }}
            imagePullPolicy: {{ $.Values.image.pullPolicy }}
            name: kube-bench
            env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            command:
              [
                "kube-bench",
                "run",
                "--targets",
                "node",
                "--benchmark",
                "eks-1.0.1",
                {{- if eq $key "asff" }}
                "--asff",
                {{- end }}
              ]
            volumeMounts:
              - name: var-lib-kubelet
                mountPath: /var/lib/kubelet
                readOnly: true
              - name: etc-systemd
                mountPath: /etc/systemd
                readOnly: true
              - name: etc-kubernetes
                mountPath: /etc/kubernetes
                readOnly: true
              {{- if eq $key "asff" }}
              - name: kube-bench-eks-config
                mountPath: "/opt/kube-bench/cfg/eks-1.0.1/config.yaml"
                subPath: config.yaml
                readOnly: true
              {{- end }}
          {{- with $.Values.nodeSelector }}
          nodeSelector:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $.Values.affinity }}
          affinity:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $.Values.tolerations }}
          tolerations:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumes:
            - name: var-lib-kubelet
              hostPath:
                path: "/var/lib/kubelet"
            - name: etc-systemd
              hostPath:
                path: "/etc/systemd"
            - name: etc-kubernetes
              hostPath:
                path: "/etc/kubernetes"
            {{- if eq $key "asff" }}
            - name: kube-bench-eks-config
              configMap:
                name: kube-bench-eks-config
                items:
                  - key: config.yaml
                    path: config.yaml
            {{- end }}
          restartPolicy: {{ default "Never" $job.restartPolicy }}
{{- end }}
