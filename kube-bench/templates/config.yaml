{{- if .Values.config.create -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: kube-bench-eks-config
  labels:
    {{- include "kube-bench.labels" . | nindent 4 }}
data:
  config.yaml: |
    AWS_ACCOUNT: "{{ .Values.audit.account }}"
    AWS_REGION: "{{ .Values.audit.region }}"
    CLUSTER_ARN: "arn:aws:eks:{{ .Values.target.region }}:{{ .Values.target.account }}:cluster/{{ .Values.target.cluster }}"
{{- end }}
