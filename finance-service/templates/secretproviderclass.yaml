{{- if .Values.secretProviderClass.create -}}
apiVersion: {{ .Values.secretProviderClass.version }}
kind: SecretProviderClass
metadata:
  name: {{ include "finance-service.secretsProviderClassName" . }}
  labels:
    {{- include "finance-service.labels" . | nindent 4 }}
  {{- with .Values.secretProviderClass.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  provider: aws
  {{- if .Values.secretProviderClass.createEnvironmentVariables }}
  secretObjects:                                 
  - secretName: {{ printf "%s-asm-secrets" (include "finance-service.secretsProviderClassName" .) }}
    type: Opaque
    labels:
        {{- include "finance-service.labels" . | nindent 6 }}
    data: 
    {{- range $secret := .Values.secretProviderClass.secrets }} 
    {{- range $env := $secret.environmentVariables }} 
    - objectName: {{ printf "%s%s" $secret.asmName ( ternary (printf ":%s" $env.secretKey) "" (ne ($env.secretKey) "SELF")) | replace "/" ":" }}
      key: {{ $env.environmentVariableName }}
    {{- end }}
    {{- end }}
  {{- end }}
  parameters:
    region: eu-west-1
    pathTranslation: ":"
    objects: |
      {{- range $secret := .Values.secretProviderClass.secrets }} 
      - objectName: "{{ $secret.asmName }}"
        objectType: secretsmanager
        {{- if $secret.environmentVariables }}
        jmesPath:
        {{- range $env := $secret.environmentVariables }}
        {{- if ne $env.secretKey "SELF" }}
            - path: "{{ $env.secretKey }}"
              objectAlias: "{{ printf "%s:%s" $secret.asmName $env.secretKey | replace "/" ":" }}"
        {{- end }}
        {{- end }}
        {{- end }}
      {{- end }}
{{- end }}
