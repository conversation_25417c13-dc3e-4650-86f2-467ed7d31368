apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "finance-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "finance-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "finance-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
