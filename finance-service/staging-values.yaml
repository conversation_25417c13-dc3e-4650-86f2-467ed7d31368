envName: staging
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_finance_service:ecaddb63d1db22bd193e2bc147e138b8d555ad77
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/staging-finance-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: finance.services.wedeliver-staging.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: staging/master-database/finance_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: staging/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: staging/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: staging/thrivve/thrivve_credentials
      environmentVariables:
        - environmentVariableName: THRIVVE_CLIENT_ID
          secretKey: CLIENT_ID
        - environmentVariableName: THRIVVE_SECRET_KEY
          secretKey: SECRET_KEY
    - asmName: staging/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: staging/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: finance-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: financeServiceDB
  - name: FLASK_ENV
    value: staging
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: STC_SERVICE
    value: http://stc-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: SUPPLIER_SERVICE
    value: http://supplier-service.services
  - name: QUICKBOOKS_REDIRECT_URI
    value: https://finance.services.wedeliver-staging.com/callback2
  - name: QUICKBOOKS_ENV
    value: sandbox
  - name: QUICKBOOKS_REALMID
    value: '4620816365207117020'
  - name: THRIVVE_INTEGRATION_URL
    value: https://thrivve.services.wedeliver-staging.com/integration
jobs:
  dsp-orders-auditing:
    suspend: true
  sdd-orders-auditing:
    suspend: true
