apiVersion: v1
kind: Service
metadata:
  name: {{ include "platform-testing-service.fullname" . }}
  labels: {{- include "platform-testing-service.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector: {{- include "platform-testing-service.selectorLabels" . | nindent 4 }}
