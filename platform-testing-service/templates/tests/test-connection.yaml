apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "platform-testing-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "platform-testing-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "platform-testing-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
