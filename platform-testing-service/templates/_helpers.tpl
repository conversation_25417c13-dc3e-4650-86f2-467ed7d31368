{{/*
Expand the name of the chart.
*/}}
{{- define "platform-testing-service.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "platform-testing-service.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "platform-testing-service.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "platform-testing-service.labels" -}}
helm.sh/chart: {{ include "platform-testing-service.chart" . }}
{{ include "platform-testing-service.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "platform-testing-service.selectorLabels" -}}
app.kubernetes.io/name: {{ include "platform-testing-service.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app: {{ include "platform-testing-service.name" . }}
team: {{ .Values.team }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "platform-testing-service.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "platform-testing-service.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the name of the secrets provider class to use
*/}}
{{- define "platform-testing-service.secretsProviderClassName" -}}
{{- if .Values.secretProviderClass.create }}
{{- default (include "platform-testing-service.fullname" .) .Values.secretProviderClass.name }}
{{- else }}
{{- default "default" .Values.secretProviderClass.name }}
{{- end }}
{{- end }}

{{/*
Conatiner volume mounts
*/}}
{{- define "platform-testing-service.volumeMounts" -}}
{{- if .Values.secretProviderClass.create }}
{{- if .Values.secretProviderClass.volumeMounts }}
{{ toYaml .Values.secretProviderClass.volumeMounts }}
{{- end }}
{{- end }}
{{- if .Values.volumeMounts }}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}

{{/*
Volumes
*/}}
{{- define "platform-testing-service.volumes" -}}
{{- if .Values.secretProviderClass.create }}
{{- if .Values.secretProviderClass.volumes }}
{{ toYaml .Values.secretProviderClass.volumes }}
{{- end }}
{{- end }}
{{- if .Values.volumeMounts -}}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}

{{/*
Envs
*/}}
{{- define "platform-testing-service.envs" -}}
{{ toYaml .Values.commonEnvironmentVariables }}
{{- if .Values.environmentVariables }}
{{ toYaml .Values.environmentVariables }}
{{- end }}
{{- if and (.Values.secretProviderClass.create) (.Values.secretProviderClass.createEnvironmentVariables) }}
{{- $k8s_secret_name := printf "%s-asm-secrets" (include "platform-testing-service.serviceAccountName" .) }}
{{- range $secret := .Values.secretProviderClass.secrets }} 
{{- range $env := $secret.environmentVariables }} 
- name: {{ $env.environmentVariableName }}
  valueFrom:
    secretKeyRef:
        name: {{ $k8s_secret_name }}
        key: {{ $env.environmentVariableName }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- define "platform-testing-service.imageSource" -}}
    {{- if .Values.imageSource }}
        {{ printf "'%s'" .Values.imageSource -}}
    {{- else }}
        {{ printf "'%s:%s'" .Values.image.repository .Values.image.tag -}}
    {{ end }}
{{- end }}
