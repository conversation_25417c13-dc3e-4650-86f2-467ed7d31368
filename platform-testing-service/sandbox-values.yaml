envName: sandbox
image:
  tag: '**********'
autoscaling:
  maxReplicas: 3
service:
  type: NodePort
  port: 80
enableDBMigration: true
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-platform-testing-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: platform-testing.services.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: sandbox/master-database/platform_testing_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: sandbox/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: sandbox/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: platform-testing-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: platformTestingDB
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: FLASK_ENV
    value: sandbox
  - name: FLASK_APP
    value: app.py
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: S3_REGION
    value: eu-west-1
  - name: S3_BUCKET
    value: wedeliver-backend-assets
  - name: DEBUG
    value: 'False'
