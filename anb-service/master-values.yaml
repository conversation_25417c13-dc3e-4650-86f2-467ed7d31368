envName: production
pdb:
  enabled: true
autoscaling:
  maxReplicas: 5
  minReplicas: 2
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-anb-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: anb.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
    - host: anb.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/master-database/anb_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: production/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: production/anb-sa/anb_sa_credentials
      environmentVariables:
        - environmentVariableName: ANB_KSA_CLIENT_ID
          secretKey: CLIENT_ID
        - environmentVariableName: ANB_KSA_CLIENT_SECRET
          secretKey: CLIENT_SECRET
    - asmName: production/anb-ps/anb_ps_credentials
      environmentVariables:
        - environmentVariableName: ANB_PS_CLIENT_ID
          secretKey: CLIENT_ID
        - environmentVariableName: ANB_PS_CLIENT_ASSERTION
          secretKey: CLIENT_ASSERTION
    - asmName: production/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: anb-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: anbServiceDB
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: FLASK_ENV
    value: production
  - name: FLASK_APP
    value: app.py
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: USE_IN_SERVER_IMAGES
    value: 'False'
  - name: PROJECT_BASE_URL
    value: https://anb.service.wedeliverapp.com
  - name: UPLOAD_FOLDER
    value: static/images
  - name: PROJECT_IMAGES_PATH
    value: ''
  - name: WEDELIVER_SUPPLIER_PROFILE_ID
    value: ''
  - name: DEFFAULT_TEAM_ID
    value: ''
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'FALSE'
  - name: FINANCE_SERVICE
    value: http://finance-service.services
  - name: SUPPLIER_SERVICE
    value: http://supplier-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: PN_SERVICE
    value: http://pn-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: WEDELIVER_SUPPLIER_ID
    value: '1'
  - name: ANB_KSA_BASE_URL
    value: https://test-api.anb.com.sa
  - name: ANB_PS_BASE_URL
    value: https://tapi.arabbank.com
  - name: ORDERING_PARTY
    value: 'Fintech A'
  - name: ORDERING_PARTY_ADDRESS_1
    value: 'Street1'
  - name: ORDERING_PARTY_ADDRESS_2
    value: 'JEDDAH'
  - name: ORDERING_PARTY_ADDRESS_3
    value: 'Saudi Arabia'
  - name: DEBIT_ACCOUNT
    value: '****************'
jobs:
  send-approved-requests:
    suspend: false
  inquiry-sent-requests:
    suspend: true
