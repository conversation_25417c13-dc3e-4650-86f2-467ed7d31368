apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "anb-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "anb-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "anb-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
