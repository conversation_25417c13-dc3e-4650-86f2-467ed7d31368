{{/*
Expand the name of the chart.
*/}}
{{- define "anb-service.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "anb-service.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "anb-service.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "anb-service.labels" -}}
helm.sh/chart: {{ include "anb-service.chart" . }}
{{ include "anb-service.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "anb-service.selectorLabels" -}}
app.kubernetes.io/name: {{ include "anb-service.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app: {{ include "anb-service.name" . }}
team: {{ .Values.team }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "anb-service.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "anb-service.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the name of the secrets provider class to use
*/}}
{{- define "anb-service.secretsProviderClassName" -}}
{{- if .Values.secretProviderClass.create }}
{{- default (include "anb-service.fullname" .) .Values.secretProviderClass.name }}
{{- else }}
{{- default "default" .Values.secretProviderClass.name }}
{{- end }}
{{- end }}

{{/*
Conatiner volume mounts
*/}}
{{- define "anb-service.volumeMounts" -}}
{{- if .Values.secretProviderClass.create }}
{{- if .Values.secretProviderClass.volumeMounts }}
{{ toYaml .Values.secretProviderClass.volumeMounts }}
{{- end }}
{{- end }}
{{- if .Values.volumeMounts }}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}

{{/*
Volumes
*/}}
{{- define "anb-service.volumes" -}}
{{- if .Values.secretProviderClass.create }}
{{- if .Values.secretProviderClass.volumes }}
{{ toYaml .Values.secretProviderClass.volumes }}
{{- end }}
{{- end }}
{{- if .Values.volumeMounts -}}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}

{{/*
Envs
*/}}
{{- define "anb-service.envs" -}}
{{ toYaml .Values.commonEnvironmentVariables }}
{{- if .Values.environmentVariables }}
{{ toYaml .Values.environmentVariables }}
{{- end }}
{{- if and (.Values.secretProviderClass.create) (.Values.secretProviderClass.createEnvironmentVariables) }}
{{- $k8s_secret_name := printf "%s-asm-secrets" (include "anb-service.secretsProviderClassName" .) }}
{{- range $secret := .Values.secretProviderClass.secrets }}
{{- range $env := $secret.environmentVariables }}
- name: {{ $env.environmentVariableName }}
  valueFrom:
    secretKeyRef:
        name: {{ $k8s_secret_name }}
        key: {{ $env.environmentVariableName }}
{{- end }}
{{- end }}
{{- end }}
{{- end }} 

{{- define "anb-service.imageSource" -}}
    {{- if .Values.imageSource }}
        {{ printf "'%s'" .Values.imageSource -}}
    {{- else }}
        {{ printf "'%s:%s'" .Values.image.repository .Values.image.tag -}}
    {{ end }}
{{- end }}

{{/*
Cron job specific volume mounts (excludes CSI secret driver volumes)
*/}}
{{- define "anb-service.cronjob.volumeMounts" -}}
{{- if .Values.volumeMounts }}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}

{{/*
Cron job specific volumes (excludes CSI secret driver volumes)
*/}}
{{- define "anb-service.cronjob.volumes" -}}
{{- if .Values.volumes }}
{{- toYaml .Values.volumes }}
{{- end }}
{{- end }}

{{/*
Cron job specific environment variables (uses K8s secrets directly, no CSI volumes)
*/}}
{{- define "anb-service.cronjob.envs" -}}
{{ toYaml .Values.commonEnvironmentVariables }}
{{- if .Values.environmentVariables }}
{{ toYaml .Values.environmentVariables }}
{{- end }}
{{- if and (.Values.secretProviderClass.create) (.Values.secretProviderClass.createEnvironmentVariables) }}
{{- $k8s_secret_name := printf "%s-asm-secrets" (include "anb-service.secretsProviderClassName" .) }}
{{- range $secret := .Values.secretProviderClass.secrets }}
{{- range $env := $secret.environmentVariables }}
- name: {{ $env.environmentVariableName }}
  valueFrom:
    secretKeyRef:
        name: {{ $k8s_secret_name }}
        key: {{ $env.environmentVariableName }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
