envName: sandbox
autoscaling:
  maxReplicas: 2
  minReplicas: 1
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-stc-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: sandbox-ingress
    alb.ingress.kubernetes.io/target-node-labels: worker_group=sandbox
  hosts:
    - host: stc.services.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
commonEnvironmentVariables:
  - name: SERVICE_NAME
    value: stc
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: DATABASE_SERVER
    value: master-database.sandbox
  - name: DATABASE_READONLY
    value: read-database-replica.sandbox
  - name: COUNTRY_CODE
    value: sa
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: sandbox/master-database/stc_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: sandbox/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: sandbox/stc/stc_credentials
      environmentVariables:
        - environmentVariableName: STC_MID
          secretKey: MERCHANT_ID
        - environmentVariableName: STC_CRT
          secretKey: CRT
        - environmentVariableName: STC_KEY
          secretKey: KEY
    - asmName: sandbox/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: stc-service-sandbox
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: stcServiceDB
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: FLASK_ENV
    value: sandbox
  - name: FLASK_APP
    value: app.py
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: AUTH_SERVICE
    value: http://auth-service-sandbox.sandbox
  - name: USE_IN_SERVER_IMAGES
    value: 'False'
  - name: PROJECT_BASE_URL
    value: https://stc.service.wedeliverapp.dev
  - name: UPLOAD_FOLDER
    value: static/images
  - name: PROJECT_IMAGES_PATH
    value: ''
  - name: WEDELIVER_SUPPLIER_PROFILE_ID
    value: ''
  - name: DEFFAULT_TEAM_ID
    value: ''
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'FALSE'
  - name: FINANCE_SERVICE
    value: http://finance-service-sandbox.sandbox
  - name: SUPPLIER_SERVICE
    value: http://supplier-service-sandbox.sandbox
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service-sandbox.sandbox
  - name: SDD_SERVICE
    value: http://sdd-service-sandbox.sandbox
  - name: PN_SERVICE
    value: http://pn-service-sandbox.sandbox
  - name: CAPTAIN_SERVICE
    value: http://captain-service-sandbox.sandbox
  - name: WEDELIVER_SUPPLIER_ID
    value: '1'
  - name: STC_BASE_URL
    value: https://sandbox.b2b.stcpay.com.sa
jobs:
  send-stc-payments:
    suspend: true
    schedule: '*/1 * * * *'
  confirm-stc-payments:
    suspend: true
    schedule: '*/2 * * * *'
  daily-statstics-stc-pedning:
    suspend: true
nodeSelector:
  worker_group: sandbox

