envName: staging
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_stc_service:40a14622d47324d16cd0a1536473ab2a095d132a
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/staging-stc-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: stc.services.wedeliver-staging.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: staging/master-database/stc_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: staging/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: staging/stc/stc_credentials
      environmentVariables:
        - environmentVariableName: STC_MID
          secretKey: MERCHANT_ID
        - environmentVariableName: STC_CRT
          secretKey: CRT
        - environmentVariableName: STC_KEY
          secretKey: KEY
    - asmName: staging/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: stc-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: stcServiceDB
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: FLASK_ENV
    value: staging
  - name: FLASK_APP
    value: app.py
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: USE_IN_SERVER_IMAGES
    value: 'False'
  - name: PROJECT_BASE_URL
    value: https://stc.service.wedeliver-staging.com
  - name: UPLOAD_FOLDER
    value: static/images
  - name: PROJECT_IMAGES_PATH
    value: ''
  - name: WEDELIVER_SUPPLIER_PROFILE_ID
    value: ''
  - name: DEFFAULT_TEAM_ID
    value: ''
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'FALSE'
  - name: FINANCE_SERVICE
    value: http://finance-service.services
  - name: SUPPLIER_SERVICE
    value: http://supplier-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: PN_SERVICE
    value: http://pn-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: WEDELIVER_SUPPLIER_ID
    value: '1'
  - name: STC_BASE_URL
    value: https://sandbox.b2b.stcpay.com.sa
jobs:
  send-stc-payments:
    suspend: false
  confirm-stc-payments:
    suspend: false
  daily-statstics-stc-pedning:
    suspend: false
