apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "stc-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "stc-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "stc-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
