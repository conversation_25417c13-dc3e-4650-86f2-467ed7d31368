envName: sandbox
autoscaling:
  maxReplicas: 2
  minReplicas: 1
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-whatsapp-service-sa-role
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: sandbox-ingress
    alb.ingress.kubernetes.io/target-node-labels: 'worker_group=sandbox'
  hosts:
    - host: whatsapp.services.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
commonEnvironmentVariables:
  - name: SERVICE_NAME
    value: whatsapp
#  - name: DATABASE_SERVER
#    value: master-database.sandbox
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: QUICKBOOKS_QBO_BASEURL
    value: https://quickbook-server.sandbox
  - name: COUNTRY_CODE
    value: sa
environmentVariables:
  - name: FLASK_ENV
    value: sandbox
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SDD_SERVICE
    value: http://sdd-service-sandbox.sandbox
  - name: AUTH_SERVICE
    value: http://auth-service-sandbox.sandbox
  - name: CAPTAIN_SERVICE
    value: http://captain-service-sandbox.sandbox
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service-sandbox.sandbox
  - name: MLCGO_DELIVERY_ID
    value: '521'
  - name: SAFEARRIVAL_DELIVERY_ID
    value: '522'
  - name: ESNAD_DELIVERY_ID
    value: '400'
  - name: ARAMEX_DELIVERY_ID
    value: '500'
nodeSelector:
  worker_group: sandbox