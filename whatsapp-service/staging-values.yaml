envName: staging
imageService: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_whatsapp_service:a7ba72003bce566121aa90bdc95b358f13d67246
autoscaling:
  maxReplicas: 3
enableDBMigration: false
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/staging-whatsapp-service-sa-role
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: whatsapp.services.wedeliver-staging.com
      paths:
        - path: /
          pathType: Prefix
environmentVariables:
  - name: FLASK_ENV
    value: staging
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: MLCGO_DELIVERY_ID
    value: '521'
  - name: SAFEARRIVAL_DELIVERY_ID
    value: '522'
  - name: ESNAD_DELIVERY_ID
    value: '400'
  - name: ARAMEX_DELIVERY_ID
    value: '500'
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_whatsapp_service:fa39c3abf6836abc849f19da83632862d10527fd
