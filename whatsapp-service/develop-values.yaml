envName: development
imageService: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_whatsapp_service:0a8231b1c1d555bb2e9eaf5f00c6501d9ba22271
autoscaling:
  maxReplicas: 3
enableDBMigration: false
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-whatsapp-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: whatsapp.services.wedeliver-dev.com
      paths:
        - path: /
          pathType: Prefix
environmentVariables:
  - name: FLASK_ENV
    value: development
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: MLCGO_DELIVERY_ID
    value: '521'
  - name: SAFEARRIVAL_DELIVERY_ID
    value: '522'
  - name: ESNAD_DELIVERY_ID
    value: '400'
  - name: ARAMEX_DELIVERY_ID
    value: '500'
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_whatsapp_service:b6cc162e02d6559860d3b944066eec3049e98a01
