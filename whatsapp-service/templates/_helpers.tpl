{{/*
Expand the name of the chart.
*/}}
{{- define "whatsapp-service.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "whatsapp-service.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "whatsapp-service.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "whatsapp-service.labels" -}}
helm.sh/chart: {{ include "whatsapp-service.chart" . }}
{{ include "whatsapp-service.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "whatsapp-service.selectorLabels" -}}
app.kubernetes.io/name: {{ include "whatsapp-service.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app: {{ include "whatsapp-service.name" . }}
team: {{ .Values.team }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "whatsapp-service.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "whatsapp-service.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the name of the secrets provider class to use
*/}}
{{- define "whatsapp-service.secretsProviderClassName" -}}
{{- if .Values.secretProviderClass.create }}
{{- default (include "whatsapp-service.fullname" .) .Values.secretProviderClass.name }}
{{- else }}
{{- default "default" .Values.secretProviderClass.name }}
{{- end }}
{{- end }}

{{/*
Conatiner volume mounts
*/}}
{{- define "whatsapp-service.volumeMounts" -}}
{{- if .Values.secretProviderClass.create }}
{{- if .Values.secretProviderClass.volumeMounts }}
{{ toYaml .Values.secretProviderClass.volumeMounts }}
{{- end }}
{{- end }}
{{- if .Values.volumeMounts }}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}

{{/*
Volumes
*/}}
{{- define "whatsapp-service.volumes" -}}
{{- if .Values.secretProviderClass.create }}
{{- if .Values.secretProviderClass.volumes }}
{{ toYaml .Values.secretProviderClass.volumes }}
{{- end }}
{{- end }}
{{- if .Values.volumeMounts -}}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}

{{/*
Envs
*/}}
{{- define "whatsapp-service.envs" -}}
{{ toYaml .Values.commonEnvironmentVariables }}
{{- if .Values.environmentVariables }}
{{ toYaml .Values.environmentVariables }}
{{- end }}
{{- if and (.Values.secretProviderClass.create) (.Values.secretProviderClass.createEnvironmentVariables) }}
{{- $k8s_secret_name := printf "%s-asm-secrets" (include "whatsapp-service.serviceAccountName" .) }}
{{- range $secret := .Values.secretProviderClass.secrets }}
{{- range $env := $secret.environmentVariables }}
- name: {{ $env.environmentVariableName }}
  valueFrom:
    secretKeyRef:
        name: {{ $k8s_secret_name }}
        key: {{ $env.environmentVariableName }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- define "whatsapp-service.imageSource" -}}
    {{- if .Values.imageSource }}
        {{ printf "'%s'" .Values.imageSource -}}
    {{- else }}
        {{ printf "'%s:%s'" .Values.image.repository .Values.image.tag -}}
    {{ end }}
{{- end }}
