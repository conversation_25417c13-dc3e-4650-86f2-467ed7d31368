apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "whatsapp-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "whatsapp-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "whatsapp-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
