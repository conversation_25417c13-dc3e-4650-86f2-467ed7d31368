server:
  nodeSelector:
    worker_group: sandbox
dashboard:
  nodeSelector:
    worker_group: sandbox

service:
  type: NodePort
  port: 80
ingress:
  hostdomain: services.wedeliverapp.dev
  annotations:
    alb.ingress.kubernetes.io/group.name: sandbox-ingress
    alb.ingress.kubernetes.io/target-node-labels: worker_group=sandbox

database:
  host: wedelivertestcluster.l0fs7.mongodb.net

serviceAccount:
  name: parse-sandbox
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-parse-sa-role
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: sandbox/mongo-database/parse_database_credentials
      environmentVariables:
        - environmentVariableName: PARSE_DATABASE_USER
          secretKey: username
        - environmentVariableName: PARSE_DATABASE_PASSWORD
          secretKey: password
    - asmName: sandbox/parse/parse_dashboard_credentials
      environmentVariables:
        - environmentVariableName: PARSE_DASHBOARD_USER
          secretKey: username
        - environmentVariableName: PARSE_DASHBOARD_PASSWORD
          secretKey: password
    - asmName: sandbox/parse/master_key
      environmentVariables:
        - environmentVariableName: PARSE_MASTER_KEY
          secretKey: SELF
    - asmName: sandbox/parse/parse_secret_configurations
      environmentVariables:
        - environmentVariableName: PARSE_SERVER_JAVASCRIPT_KEY
          secretKey: javascriptKey
        - environmentVariableName: PARSE_SERVER_CLIENT_KEY
          secretKey: clientKey
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: parse-sandbox
