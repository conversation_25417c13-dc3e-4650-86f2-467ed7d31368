CHART NAME: {{ .Chart.Name }}
CHART VERSION: {{ .Chart.Version }}
APP VERSION: {{ .Chart.AppVersion }}

** Please be patient while the chart is being deployed **

Parse Server
------------

1. Get your Parse Server URL:

{{- if .Values.ingress.enabled }}

  You should be able to access Parse Server through: {{ if .Values.ingress.tls }}https{{ else }}http{{ end }}://{{ .Values.ingress.server.hostname }}
  For instance:

  export SERVICE_HOST={{ .Values.ingress.server.hostname }}

{{- else if contains "NodePort" .Values.service.type }}

  export NODE_PORT=$(kubectl get svc --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" {{ include "common.names.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  export SERVICE_HOST=$NODE_IP:$NODE_PORT

{{- else if contains "LoadBalancer" .Values.service.type }}

  NOTE: It may take a few minutes for the LoadBalancer IP to be available.
        Watch the status with: 'kubectl get svc --namespace {{ .Release.Namespace }} -w {{ include "common.names.fullname" . }}'

  export SERVICE_HOST=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "common.names.fullname" . }} --template "{{ "{{ range (index .status.loadBalancer.ingress 0) }}{{ . }}{{ end }}" }}"):{{ .Values.server.port }}

{{- else if contains "ClusterIP"  .Values.service.type }}

  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l app.kubernetes.io/name={{ template "common.names.name" . }},app.kubernetes.io/component=server -o jsonpath="{.items[0].metadata.name}")
  kubectl port-forward --namespace {{ .Release.Namespace }} $POD_NAME 1337:1337 &
  export SERVICE_HOST=127.0.0.1:1337

{{- end }}

Example Usage:

  curl -X POST \
    -H "X-Parse-Application-Id: {{ .Values.server.appId }}" \
    -H "Content-Type: application/json" \
    -d '{"score":1337,"playerName":"Sean Plott","cheatMode":false}' \
    http://$SERVICE_HOST{{ .Values.server.mountPath }}/classes/GameScore

{{ if .Values.dashboard.enabled -}}
Parse Dashboard
---------------
{{- if empty (include "parse.host" .) }}
###############################################################################
### ERROR: You did not provide an external host in your 'helm install' call ###
###############################################################################

Your Parse Dashboard deployment will be incomplete until you configure your
Parse Server with a resolvable host. To configure Parse with the URL of your
service:

1. Get the Parse Server URL by running:

  NOTE: It may take a few minutes for the LoadBalancer IP to be available.
        Watch the status with: 'kubectl get svc --namespace {{ .Release.Namespace }} -w {{ include "common.names.fullname" . }}'

  export APP_HOST=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "common.names.fullname" . }} --template "{{ "{{ range (index .status.loadBalancer.ingress 0) }}{{ . }}{{ end }}" }}")

2. Complete your Parse Dashboard deployment by running:

  helm upgrade --namespace {{ .Release.Namespace }} {{ .Release.Name }} bitnami/{{ .Chart.Name }} \
    --set server.host=$APP_HOST,server.port={{ .Values.server.port }},server.masterKey=$(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.fullname" . }} -o jsonpath="{.data.master-key}" | base64 --decode),dashboard.username={{ .Values.dashboard.username }},dashboard.password=$(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.fullname" . }} -o jsonpath="{.data.parse-dashboard-password}" | base64 --decode){{- if .Values.global }}{{- if .Values.global.imagePullSecrets }},global.imagePullSecrets={{ .Values.global.imagePullSecrets }}{{- end }}{{- end }}

{{- else }}

1. Get the Parse Dashboard URL:

{{- if .Values.ingress.enabled }}

  You should be able to access Parse Dashboard through: {{ if .Values.ingress.tls }}https{{ else }}http{{ end }}://{{ .Values.ingress.dashboard.hostname }}

{{- else if eq .Values.service.type "ClusterIP" }}

  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l app.kubernetes.io/name={{ template "common.names.name" . }},app.kubernetes.io/component=dashboard -o jsonpath="{.items[0].metadata.name}")
  kubectl port-forward --namespace {{ .Release.Namespace }} $POD_NAME 4040:4040 &
  echo "Parse Dashboard URL: http://127.0.0.1:4040/"

{{- else }}

  echo "Parse Dashboard URL: http://{{ include "parse.host" . }}/"

{{- end }}

2. Get your Parse Dashboard login credentials by running:

  echo Username: {{ .Values.dashboard.username }}
  echo Password: $(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.fullname" . }} -o jsonpath="{.data.parse-dashboard-password}" | base64 --decode)
{{- end }}
{{- end }}

{{ include "parse.validateValues" . }}
{{ include "parse.checkRollingTags" . }}
