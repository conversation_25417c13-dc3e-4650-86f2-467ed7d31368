{{- if and (include "parse.host" .) .Values.dashboard.enabled -}}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.fullname" . }}-dashboard
  labels: {{ include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: dashboard
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels: {{ include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: dashboard
  replicas: 1
  template:
    metadata:
      labels: {{ include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: dashboard
    spec:
      serviceAccountName: {{ .Values.serviceAccount.name }}
      {{- if .Values.dashboard.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.dashboard.podAffinityPreset "component" "dashboard" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.dashboard.podAntiAffinityPreset "component" "dashboard" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.dashboard.nodeAffinityPreset.type "key" .Values.dashboard.nodeAffinityPreset.key "values" .Values.dashboard.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.dashboard.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dashboard.securityContext.enabled }}
      securityContext:
        fsGroup: {{ .Values.dashboard.securityContext.fsGroup }}
        runAsUser: {{ .Values.dashboard.securityContext.runAsUser }}
      {{- end }}
      {{- include "parse.imagePullSecrets" . | nindent 6 }}
      containers:
        - name: parse-dashboard
          image: {{ include "parse.dashboard.image" . }}
          imagePullPolicy: {{ .Values.dashboard.image.pullPolicy | quote }}
          env:
            - name: PARSE_HOST
              value: {{ include "parse.host" . | quote }}
            - name: PARSE_USE_HOSTNAME
              value: {{ ternary "yes" "no" .Values.ingress.enabled | quote }}
            - name: PARSE_PORT_NUMBER
              value: {{ include "parse.external-port" . | quote }}
            - name: PARSE_PROTOCOL
              value: {{ .Values.dashboard.parseServerUrlProtocol | quote }}
            - name: PARSE_APP_ID
              value: {{ .Values.server.appId | quote }}
            - name: PARSE_DASHBOARD_APP_NAME
              value: {{ .Values.dashboard.appName | quote }}
            {{- if and (.Values.secretProviderClass.create) (.Values.secretProviderClass.createEnvironmentVariables) }}
            {{- $k8s_secret_name := printf "%s-asm-secrets" .Values.secretProviderClass.name }}
            {{- range $secret := .Values.secretProviderClass.secrets }} 
            {{- range $env := $secret.environmentVariables }} 
            - name: {{ $env.environmentVariableName }}
              valueFrom:
                secretKeyRef:
                    name: {{ $k8s_secret_name }}
                    key: {{ $env.environmentVariableName }}
            {{- end }}
            {{- end }}
            {{- end }}
            {{- if .Values.dashboard.extraEnvVars }}
              {{- include "common.tplvalues.render" ( dict "value" .Values.dashboard.extraEnvVars "context" $ ) | nindent 12 }}
            {{- end }}
          {{- if or .Values.dashboard.extraEnvVarsCM .Values.dashboard.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.dashboard.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" ( dict "value" .Values.dashboard.extraEnvVarsCM "context" $ ) }}
            {{- end }}
            {{- if .Values.dashboard.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" ( dict "value" .Values.dashboard.extraEnvVarsSecret "context" $ ) }}
            {{- end }}
          {{- end }}
          ports:
            - name: dashboard-http
              containerPort: 4040
          {{- if and .Values.dashboard.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /
              port: dashboard-http
            initialDelaySeconds: {{ .Values.dashboard.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.dashboard.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.dashboard.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.dashboard.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.dashboard.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if and .Values.dashboard.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /
              port: dashboard-http
            initialDelaySeconds: {{ .Values.dashboard.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.dashboard.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.dashboard.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.dashboard.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.dashboard.readinessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.dashboard.resources }}
          resources: {{- toYaml .Values.dashboard.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: parse-dashboard-data
              mountPath: /bitnami/parse-dashboard
            {{- if .Values.secretProviderClass.create }}
            {{- if .Values.secretProviderClass.volumeMounts }}
            {{- toYaml .Values.secretProviderClass.volumeMounts | nindent 12 }}
            {{- end }}
            {{- end }}
      volumes:
        - name: parse-dashboard-data
          emptyDir: {}
        {{- if .Values.secretProviderClass.create }}
        {{- if .Values.secretProviderClass.volumes }}
        {{- toYaml .Values.secretProviderClass.volumes | nindent 8 }}
        {{- end }}
        {{- end }}
{{- end -}}
