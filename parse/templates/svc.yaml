apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.fullname" . }}
  labels: {{ include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  {{- if and (.Values.service.loadBalancerIP) (eq .Values.service.type "LoadBalancer") }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP | quote }}
  {{- end }}
  {{- if (or (eq .Values.service.type "LoadBalancer") (eq .Values.service.type "NodePort")) }}
  externalTrafficPolicy: {{ .Values.service.externalTrafficPolicy | quote }}
  {{- end }}
  ports:
    - name: server-http
      port: {{ .Values.server.port }}
      targetPort: server-http
    - name: dashboard-http
      port: {{ .Values.service.port }}
      targetPort: dashboard-http
      {{- if (and (eq .Values.service.type "NodePort") (not (empty .Values.service.nodePorts.http))) }}
      nodePort: {{ .Values.service.nodePorts.http }}
      {{- end }}
  selector: {{ include "common.labels.matchLabels" . | nindent 4 }}
