apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "common.names.fullname" . }}-server
  labels: {{ include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: server
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels: {{ include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: server
  replicas: 1
  template:
    metadata:
      labels: {{ include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: server
    spec:
      serviceAccountName: {{ .Values.serviceAccount.name }}
      {{- if .Values.server.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.server.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.server.podAffinityPreset "component" "server" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.server.podAntiAffinityPreset "component" "server" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.server.nodeAffinityPreset.type "key" .Values.server.nodeAffinityPreset.key "values" .Values.server.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.server.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.server.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.server.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.server.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.server.securityContext.enabled }}
      securityContext:
        fsGroup: {{ .Values.server.securityContext.fsGroup }}
        runAsUser: {{ .Values.server.securityContext.runAsUser }}
      {{- end }}
      {{- include "parse.imagePullSecrets" . | nindent 6 }}
      containers:
        - name: parse
          image: {{ include "parse.server.image" . }}
          imagePullPolicy: {{ .Values.server.image.pullPolicy | quote }}
          env:
            - name: BITNAMI_DEBUG
              value: 'true'
            - name: PARSE_ENABLE_HTTPS
              value: "yes"
            - name: PARSE_HOST
              value: {{ include "parse.host" . | quote }}
            - name: PARSE_PORT_NUMBER
              value: {{ .Values.server.port | quote }}
            - name: PARSE_MOUNT_PATH
              value: {{ .Values.server.mountPath | quote }}
            - name: PARSE_APP_ID
              value: {{ .Values.server.appId | quote }}
            - name: PARSE_DATABASE_HOST
              value: {{ .Values.database.host | quote }}
            - name: PARSE_DATABASE_NAME
              value: {{ .Values.database.name | quote }}
            - name: PARSE_DATABASE_PORT_NUMBER
              value: {{ .Values.database.port | quote }}
            - name: PARSE_ENABLE_CLOUD_CODE
              value: {{ ternary "yes" "no" .Values.server.enableCloudCode | quote }}
            - name: PARSE_SERVER_CLOUD
              value: /opt/bitnami/parse/cloud/main.js
            {{- if and (.Values.secretProviderClass.create) (.Values.secretProviderClass.createEnvironmentVariables) }}
            {{- $k8s_secret_name := printf "%s-asm-secrets" .Values.secretProviderClass.name }}
            {{- range $secret := .Values.secretProviderClass.secrets }} 
            {{- range $env := $secret.environmentVariables }} 
            - name: {{ $env.environmentVariableName }}
              valueFrom:
                secretKeyRef:
                    name: {{ $k8s_secret_name }}
                    key: {{ $env.environmentVariableName }}
            {{- end }}
            {{- end }}
            {{- end }}
            {{- if .Values.server.extraEnvVars }}
              {{- include "common.tplvalues.render" ( dict "value" .Values.server.extraEnvVars "context" $ ) | nindent 12 }}
            {{- end }}
          {{- if or .Values.server.extraEnvVarsCM .Values.server.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.server.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" ( dict "value" .Values.server.extraEnvVarsCM "context" $ ) }}
            {{- end }}
            {{- if .Values.server.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" ( dict "value" .Values.server.extraEnvVarsSecret "context" $ ) }}
            {{- end }}
          {{- end }}
          ports:
            - name: server-http
              containerPort: {{ .Values.server.port }}
          {{- if and .Values.server.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.server.mountPath }}/users
              port: server-http
              httpHeaders:
                - name: X-Parse-Application-Id
                  value: {{ .Values.server.appId }}
            initialDelaySeconds: {{ .Values.server.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.server.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.server.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.server.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.server.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if and .Values.server.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.server.mountPath }}/users
              port: server-http
              httpHeaders:
                - name: X-Parse-Application-Id
                  value: {{ .Values.server.appId }}
            initialDelaySeconds: {{ .Values.server.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.server.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.server.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.server.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.server.readinessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.server.resources }}
          resources: {{- toYaml .Values.server.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
#            - name: parse-data
#              mountPath: /bitnami/parse
            {{- if and .Values.server.enableCloudCode (or (.Files.Glob "files/cloud/*.js") .Values.server.cloudCodeScripts .Values.server.existingCloudCodeCM) }}
            - name: cloud-code-config
              mountPath: /opt/bitnami/parse/cloud
            {{- end }}
            {{- if .Values.secretProviderClass.create }}
            {{- if .Values.secretProviderClass.volumeMounts }}
            {{- toYaml .Values.secretProviderClass.volumeMounts | nindent 12 }}
            {{- end }}
            {{- end }}
      volumes:
        {{- if and .Values.server.enableCloudCode (or (.Files.Glob "files/cloud/*.js") .Values.server.cloudCodeScripts .Values.server.existingCloudCodeCM) }}
        - name: cloud-code-config
          configMap:
            name: {{ include "parse.cloudCodeScriptsCMName" . }}
        {{- end }}
#        - name: parse-data
#        {{- if .Values.persistence.enabled }}
#          persistentVolumeClaim:
#            claimName: {{ include "common.names.fullname" . }}
#        {{- else }}
#          emptyDir: {}
#        {{- end }}
        {{- if .Values.secretProviderClass.create }}
        {{- if .Values.secretProviderClass.volumes }}
        {{- toYaml .Values.secretProviderClass.volumes | nindent 8 }}
        {{- end }}
        {{- end }}
