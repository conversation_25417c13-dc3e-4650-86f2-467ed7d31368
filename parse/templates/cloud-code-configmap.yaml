{{- if and .Values.server.enableCloudCode (or .Values.server.cloudCodeScripts (.Files.Glob "files/cloud/*.js")) (not .Values.server.existingCloudCodeCM) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.fullname" . }}-cloud-code-scripts
  labels: {{ include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: server
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
{{- with .Files.Glob "files/cloud/*.js" }}
{{ .AsConfig | indent 2 }}
{{- end }}
{{- if .Values.server.cloudCodeScripts }}
{{- include "common.tplvalues.render" (dict "value" .Values.server.cloudCodeScripts "context" $) | nindent 2 }}
{{- end }}
{{- end }}
