service:
  type: NodePort
  port: 80
ingress:
  hostdomain: services.wedeliverapp.com

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-parse-sa-role

database:
  host: SG-parse-67258.servers.mongodirector.com
  # host: SG-icy-roar-7186-66652.servers.mongodirector.com
  # host: docdb-2024-08-20-12-35-22.cxuo2upvdyyc.eu-west-1.docdb.amazonaws.com
  # host: docdb-2024-08-20-12-35-22.cluster-cxuo2upvdyyc.eu-west-1.docdb.amazonaws.com
  # host: cluster0.grozi.mongodb.net

secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/mongo-database/parse_database_credentials
      environmentVariables:
        - environmentVariableName: PARSE_DATABASE_USER
          secretKey: username
        - environmentVariableName: PARSE_DATABASE_PASSWORD
          secretKey: password
    - asmName: production/parse/parse_dashboard_credentials
      environmentVariables:
        - environmentVariableName: PARSE_DASHBOARD_USER
          secretKey: username
        - environmentVariableName: PARSE_DASHBOARD_PASSWORD
          secretKey: password
    - asmName: production/parse/master_key
      environmentVariables:
        - environmentVariableName: PARSE_MASTER_KEY
          secretKey: SELF
    - asmName: production/parse/parse_secret_configurations
      environmentVariables:
        - environmentVariableName: PARSE_SERVER_JAVASCRIPT_KEY
          secretKey: javascriptKey
        - environmentVariableName: PARSE_SERVER_CLIENT_KEY
          secretKey: clientKey
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: parse
