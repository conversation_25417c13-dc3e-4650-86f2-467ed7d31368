## @section Global parameters
## Global Docker image parameters
## Please, note that this will override the image parameters, including dependencies, configured to use the global value
## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass

## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker registry secret names as an array
## @param global.storageClass Global StorageClass for Persistent Volume(s)
##
global:
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  storageClass: parse-server

## @section Common Parameters

## @param nameOverride String to partially override common.names.fullname template (will maintain the release name)
##
nameOverride: ""
## @param fullnameOverride String to fully override common.names.fullname template
##
fullnameOverride: ""
## @param kubeVersion Force target Kubernetes version (using Helm capabilites if not set)
##
kubeVersion: ""
## @param commonLabels Add labels to all the deployed resources
##
commonLabels: {}
## @param commonAnnotations Add annotations to all the deployed resources
##
commonAnnotations: {}
## @param extraDeploy Array of extra objects to deploy with the release
##
extraDeploy: []

## @section Parse server parameters

## loadBalancerIP for the Parse Service (optional, cloud specific)
## ref: https://kubernetes.io/docs/user-guide/services/#type-loadbalancer
##
server:
  ## Bitnami Parse image version
  ## ref: https://hub.docker.com/r/bitnami/parse/tags/
  ## @param server.image.registry Parse image registry
  ## @param server.image.repository Parse image repository
  ## @param server.image.tag Parse image tag (immutable tags are recommended)
  ## @param server.image.pullPolicy Image pull policy
  ## @param server.image.pullSecrets Specify docker-registry secret names as an array
  ##
  image:
    registry: docker.io
    repository: bitnami/parse
    # repository: public.ecr.aws/wedeliver/parse_server_image
    tag: 5
    # tag: "v1.0"
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
    debug: true
  ## @param server.hostAliases Deployment pod host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## Parse Server Pod Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  ## @param server.securityContext.enabled Enable security context for Parse Server
  ## @param server.securityContext.fsGroup Group ID for Parse Server container
  ## @param server.securityContext.runAsUser User ID for Parse Server container
  ##
  securityContext:
    enabled: true
    fsGroup: 0
    runAsUser: 0

  ## @param server.port Parse server port
  ## ref: https://github.com/bitnami/bitnami-docker-parse#configuration
  ##
  port: 1337
  ## @param server.mountPath Parse server API mount path
  ## ref: https://github.com/bitnami/bitnami-docker-parse#configuration
  ##
  mountPath: /parse
  ## @param server.appId Parse server App ID
  ## ref: https://github.com/bitnami/bitnami-docker-parse#configuration
  ##
  appId: wedeliver-tracker
  ## @param server.masterKey Parse server Master Key
  ## ref: https://github.com/bitnami/bitnami-docker-parse#configuration
  ##
  masterKey: th1sShouldBeS3cr3t!
  ## @param server.extraEnvVars An array to add extra env vars
  ## For example:
  ## extraEnvVars:
  ##  - name: PARSE_SERVER_ALLOW_CLIENT_CLASS_CREATION
  ##    value: "true"
  ##
  extraEnvVars: []
  ## @param server.extraEnvVarsCM Name of a ConfigMap containing extra environment variables
  ##
  extraEnvVarsCM: ""
  ## @param server.extraEnvVarsSecret Name of a Secret containing extra environment variables
  ##
  extraEnvVarsSecret: ""
  ## @param server.enableCloudCode Enable Parse Cloud Code
  ## ref: https://github.com/bitnami/bitnami-docker-parse#how-to-deploy-your-cloud-functions-with-parse-cloud-code
  ##
  enableCloudCode: yes
  ## @param server.cloudCodeScripts Cloud Code scripts
  ## Specify dictionary of Cloud Code scripts and content
  ## Alternatively, you can put your scripts under the files/cloud directory
  ##
  ## cloudCodeScripts:
  ##   main.js: |
  ##      Parse.Cloud.define("sayHelloWorld", function(request, response) {
  ##        return "Hello world!";
  ##      });
  ##
  cloudCodeScripts: {}
  ## @param server.existingCloudCodeScriptsCM ConfigMap with Cloud Code scripts (Note: Overrides `cloudCodeScripts`).
  ## NOTE: This will override cloudCodeScripts
  ##
  existingCloudCodeScriptsCM: ""
  ## @param server.resources Parse Server pods' resource requests and limits
  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/
  ##
  resources: {}
  ## Parse Server pods' liveness probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes)
  ## @param server.livenessProbe.enabled Enable livenessProbe
  ## @param server.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param server.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param server.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param server.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param server.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: false
    initialDelaySeconds: 120
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## Parse Server pods' readiness probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes)
  ## @param server.readinessProbe.enabled Enable readinessProbe
  ## @param server.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param server.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param server.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param server.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param server.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param server.podAffinityPreset Parse server pod affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param server.podAntiAffinityPreset Parse server pod anti-affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param server.nodeAffinityPreset.type Parse server node affinity preset type. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param server.nodeAffinityPreset.key Parse server node label key to match Ignored if `affinity` is set.
    ## E.g.
    ## key: "kubernetes.io/e2e-az-name"
    ##
    key: ""
    ## @param server.nodeAffinityPreset.values Parse server node label values to match. Ignored if `affinity` is set.
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param server.affinity Parse server affinity for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## Note: server.podAffinityPreset, server.podAntiAffinityPreset, and server.nodeAffinityPreset will be ignored when it's set
  ##
  affinity: {}
  ## @param server.nodeSelector Parse server node labels for pod assignment
  ## ref: https://kubernetes.io/docs/user-guide/node-selection/
  ##
  nodeSelector:
    worker_group: services
  ## @param server.tolerations Parse server tolerations for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []

## @section Traffic Exposure Parameters

## Kubernetes serviceType for Parse Deployment
## ref: https://kubernetes.io/docs/user-guide/services/#publishing-services---service-types
##
service:
  ## @param service.type Kubernetes Service type
  ##
  type: NodePort
  ## @param service.port Service HTTP port (Dashboard)
  ##
  port: 80
  ## loadBalancerIP:
  ## @param service.nodePorts.http Kubernetes HTTP node port
  ## nodePorts:
  ##   http: <to set explicitly, choose port between 30000-32767>
  ##
  nodePorts:
    http: ""
  ## @param service.externalTrafficPolicy Enable client source IP preservation
  ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
  ##
  externalTrafficPolicy: Cluster
## Configure the ingress resource that allows you to access the Parse installation.
## ref: https://kubernetes.io/docs/user-guide/ingress/
##
ingress:
  ## @param ingress.enabled Set to true to enable ingress record generation
  ##
  enabled: true
  ## DEPRECATED: Use ingress.annotations instead of ingress.certManager
  ## certManager: false
  ##

  ## @param ingress.tls Enable TLS configuration for the hostname defined at ingress.hostname parameter
  ## TLS certificates will be retrieved from a TLS secret with name: {{- printf "%s-tls" .Values.ingress.hostname }}
  ## You can use the ingress.secrets parameter to create this TLS secret or relay on cert-manager to create it
  ##
  tls: false
  ## @param ingress.annotations Additional annotations for the Ingress resource. To enable certificate autogeneration, place here your cert-manager annotations.
  ## For a full list of possible ingress annotations, please see
  ## ref: https://github.com/kubernetes/ingress-nginx/blob/master/docs/user-guide/nginx-configuration/annotations.md
  ## Use this parameter to set the required annotations for cert-manager, see
  ## ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
  ##
  ## e.g:
  ## annotations:
  ##   kubernetes.io/ingress.class: nginx
  ##   cert-manager.io/cluster-issuer: cluster-issuer-name
  ##
  className: ""
  hostdomain: local
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: services-ingress
    alb.ingress.kubernetes.io/healthcheck-path: /health_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: 'worker_group=services'
  dashboard:
    ## @param ingress.dashboard.hostname Default host for the ingress resource
    ##
    hostname: parse-dashboard
    ## @param ingress.dashboard.path The Path to WordPress. You may need to set this to '/*' in order to use this with ALB ingress controllers.
    ##
    path: /
    ## @param ingress.dashboard.pathType Ingress path type
    ##
    pathType: Prefix
    ## @param ingress.dashboard.extraHosts The list of additional hostnames to be covered with this ingress record.
    ## Most likely the hostname above will be enough, but in the event more hosts are needed, this is an array
    ## extraHosts:
    ## - name: wordpress.local
    ##   path: /
    ##
    extraHosts: []
    ## @param ingress.dashboard.extraPaths Any additional arbitrary paths that may need to be added to the ingress under the main host.
    ## For example: The ALB ingress controller requires a special rule for handling SSL redirection.
    ## extraPaths:
    ## - path: /*
    ##   backend:
    ##     serviceName: ssl-redirect
    ##     servicePort: use-annotation
    ##
    extraPaths: []
  server:
    ## @param ingress.server.hostname Default host for the ingress resource
    ##
    hostname: parse-server
    ## @param ingress.server.path Default path for the ingress resource*' in order to use this with ALB ingress controllers.
    ##
    path: /
    ## @param ingress.server.pathType Ingress path type
    ##
    pathType: Prefix
    ## @param ingress.server.extraHosts The list of additional hostnames to be covered with this ingress record.
    ## Most likely the hostname above will be enough, but in the event more hosts are needed, this is an array
    ## extraHosts:
    ## - name: wordpress.local
    ##   path: /
    ##
    extraHosts: []
    ## @param ingress.server.extraPaths Any additional arbitrary paths that may need to be added to the ingress under the main host.
    ## For example: The ALB ingress controller requires a special rule for handling SSL redirection.
    ## extraPaths:
    ## - path: /*
    ##   backend:
    ##     serviceName: ssl-redirect
    ##     servicePort: use-annotation
    ##
    extraPaths: []
  ## @param ingress.extraTls The tls configuration for additional hostnames to be covered with this ingress record.
  ## see: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls
  ## extraTls:
  ## - hosts:
  ##     - wordpress.local
  ##   secretName: wordpress.local-tls
  ##
  extraTls: []
  ## @param ingress.secrets If you're providing your own certificates, please use this to add the certificates as secrets
  ## key and certificate should start with -----BEGIN CERTIFICATE----- or
  ## -----BEGIN RSA PRIVATE KEY-----
  ##
  ## name should line up with a tlsSecret set further up
  ## If you're using cert-manager, this is unneeded, as it will create the secret for you if it is not set
  ##
  ## It is also possible to create and manage the certificates outside of this helm chart
  ## Please see README.md for more information
  ## e.g:
  ## - name: parse.local-tls
  ##   key:
  ##   certificate:
  ##
  secrets: []

## @section Persistence Parameters

## Enable persistence using Persistent Volume Claims
## ref: https://kubernetes.io/docs/user-guide/persistent-volumes/
##
persistence:
  ## @param persistence.enabled Enable Parse persistence using PVC
  ##
  enabled: false
  ## @param persistence.storageClass PVC Storage Class for Parse volume
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  storageClass: ""
  ## @param persistence.accessMode PVC Access Mode for Parse volume
  ##
  accessMode: ReadWriteOnce
  ## @param persistence.size PVC Storage Request for Parse volume
  ##
  size: 8Gi

## @section Dashboard Parameters

dashboard:
  ## @param dashboard.enabled Enable parse dashboard
  ##
  enabled: true
  ## Bitnami Parse Dashboard image version
  ## ref: https://hub.docker.com/r/bitnami/parse-dashboard/tags/
  ## @param dashboard.image.registry Dashboard image registry
  ## @param dashboard.image.repository Dashboard image repository
  ## @param dashboard.image.tag Dashboard image tag (immutable tags are recommended)
  ## @param dashboard.image.pullPolicy Image pull policy
  ## @param dashboard.image.pullSecrets Specify docker-registry secret names as an array
  ##
  image:
    registry: docker.io
    repository: bitnami/parse-dashboard
    tag: 3.3.0-debian-10-r91
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param dashboard.hostAliases Deployment pod host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## Parse Dashboard Pod Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  ## @param dashboard.securityContext.enabled Enable security context for Dashboard
  ## @param dashboard.securityContext.fsGroup Group ID for Dashboard container
  ## @param dashboard.securityContext.runAsUser User ID for Dashboard container
  ##
  securityContext:
    enabled: true
    fsGroup: 1001
    runAsUser: 1001
  ## @param dashboard.username Parse Dashboard application username
  ## ref: https://github.com/bitnami/bitnami-docker-parse-dashboard#configuration
  ##
  username: user
  ## @param dashboard.password Parse Dashboard application password
  ## Defaults to a random 10-character alphanumeric string if not set
  ## ref: https://github.com/bitnami/bitnami-docker-parse-dashboard#configuration
  ##
  password: user123
  ## @param dashboard.appName Parse Dashboard application name
  ## ref: https://github.com/bitnami/bitnami-docker-parse-dashboard#configuration
  ##
  appName: wedeliver-dahsboard
  ## @param dashboard.resources Parse Dashboard pods' resource requests and limits
  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/
  ##
  resources: {}
  ## Parse Dashboard pods' liveness probe
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes)
  ## @param dashboard.livenessProbe.enabled Enable livenessProbe
  ## @param dashboard.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param dashboard.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param dashboard.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param dashboard.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param dashboard.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    initialDelaySeconds: 240
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## Parse Dashboard pods' liveness probe
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes)
  ## @param dashboard.readinessProbe.enabled Enable readinessProbe
  ## @param dashboard.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param dashboard.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param dashboard.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param dashboard.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param dashboard.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param dashboard.podAffinityPreset Parse dashboard pod affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param dashboard.podAntiAffinityPreset Parse dashboard pod anti-affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ## Allowed values: soft, hard
  ##
  nodeAffinityPreset:
    ## @param dashboard.nodeAffinityPreset.type Parse dashboard node affinity preset type. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param dashboard.nodeAffinityPreset.key Parse dashboard node label key to match Ignored if `affinity` is set.
    ## E.g.
    ## key: "kubernetes.io/e2e-az-name"
    ##
    key: ""
    ## @param dashboard.nodeAffinityPreset.values Parse dashboard node label values to match. Ignored if `affinity` is set.
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param dashboard.affinity Parse dashboard affinity for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## Note: dashboard.podAffinityPreset, dashboard.podAntiAffinityPreset, and dashboard.nodeAffinityPreset will be ignored when it's set
  ##
  affinity: {}
  ## @param dashboard.nodeSelector Parse dashboard node labels for pod assignment
  ## ref: https://kubernetes.io/docs/user-guide/node-selection/
  ##
  nodeSelector:
    worker_group: services
  ## @param dashboard.tolerations Parse dashboard tolerations for pod assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param dashboard.parseServerUrlProtocol Protocol used by Parse Dashboard to form the URLs to Parse server
  ##
  parseServerUrlProtocol: "https"
  ## @param dashboard.extraEnvVars An array to add extra env vars
  ## For example:
  ## extraEnvVars:
  ##  - name: KIBANA_ELASTICSEARCH_URL
  ##    value: test
  ##
  extraEnvVars: []
  ## @param dashboard.extraEnvVarsCM Name of a ConfigMap containing extra environment variables
  ##
  extraEnvVarsCM: ""
  ## @param dashboard.extraEnvVarsSecret Name of a Secret containing extra environment variables
  ##
  extraEnvVarsSecret: ""

## @section Volume Permissions parameters

## Init containers parameters:
## volumePermissions: Change the owner and group of the persistent volume mountpoint to runAsUser:fsGroup values from the securityContext section.
##
volumePermissions:
  ## @param volumePermissions.enabled Enable init container that changes volume permissions in the data directory (for cases where the default k8s `runAsUser` and `fsUser` values do not work)
  ##
  enabled: true
  ## @param volumePermissions.image.registry Init container volume-permissions image registry
  ## @param volumePermissions.image.repository Init container volume-permissions image name
  ## @param volumePermissions.image.tag Init container volume-permissions image tag
  ## @param volumePermissions.image.pullPolicy Init container volume-permissions image pull policy
  ## @param volumePermissions.image.pullSecrets Init container volume-permissions image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/bitnami-shell
    tag: 10-debian-10-r387
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param volumePermissions.resources The resources for the container
  ##
  resources: {}

  ## Enable persistence using Persistent Volume Claims
  ## ref: https://kubernetes.io/docs/user-guide/persistent-volumes/
  ##
  persistence:
    ## @param mongodb.persistence.enabled Enable MongoDB&reg; persistence using PVC
    ##
    enabled: true
    ## @param mongodb.persistence.storageClass PVC Storage Class for MongoDB&reg; volume
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack)
    ##
    storageClass: ""
    ## @param mongodb.persistence.accessMode PVC Access Mode for MongoDB&reg; volume
    ##
    accessMode: ReadWriteOnce
    ## @param mongodb.persistence.size PVC Storage Request for MongoDB&reg; volume
    ##
    size: 8Gi

secretProviderClass:
  # Specifies whether a secrets provider class should be created
  create: false
  # Annotations to add to the secrets provider class
  annotations: {}
  # The name of the secrets provider class to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "parse"
  # Provide the secrets, full path name, list.
  secrets: []

serviceAccount:
  create: true
  name: "parse"

database:
 host: ""
 name: parse
 port: 27017
