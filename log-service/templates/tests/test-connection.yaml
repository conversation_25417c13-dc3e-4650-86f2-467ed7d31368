apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "log-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "log-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "log-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
