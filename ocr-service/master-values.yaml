envName: production
pdb:
  enabled: true
autoscaling:
  maxReplicas: 5
  minReplicas: 2
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-ocr-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: ocr.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
environmentVariables:
  - name: FLASK_ENV
    value: production
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: MLCGO_DELIVERY_ID
    value: '521'
  - name: SAFEARRIVAL_DELIVERY_ID
    value: '522'
  - name: ESNAD_DELIVERY_ID
    value: '400'
  - name: ARAMEX_DELIVERY_ID
    value: '500'
