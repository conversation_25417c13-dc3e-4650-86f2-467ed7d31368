# Default values for ocr-service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

team: backend-team

nameOverride: ""
fullnameOverride: ""

replicaCount: 1
revisionHistoryLimit: 10

maxUnavailable: 25%
maxSurge: 25%

progressDeadlineSeconds: 60
terminationGracePeriodSeconds: 30

image:
  repository: 026392744560.dkr.ecr.eu-west-1.amazonaws.com/backend_ocr_service
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: bb671fd9692299969e3fc3e036dcdc78fc9dca1c

imagePullSecrets: []

enableDBMigration: false

containerPorts:
  - containerPort: 8000
    name: http
    protocol: TCP

livenessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 30
  timeoutSeconds: 15
  periodSeconds: 15
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 30
  timeoutSeconds: 15
  periodSeconds: 15
  successThreshold: 1
  failureThreshold: 3

commonEnvironmentVariables:
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: SERVICE_NAME
    value: ocr
#  - name: DATABASE_SERVER
#    value: master-database.default
#  - name: BOOTSTRAP_SERVERS
#    value: kafka-server.default:9092
  - name: QUICKBOOKS_QBO_BASEURL
    value: https://quickbook-server.default
  - name: COUNTRY_CODE
    value: sa

volumeMounts: []
volumes: []

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

secretProviderClass:
  # Specifies whether a secrets provider class should be created
  create: false
  # Annotations to add to the secrets provider class
  annotations: {}
  # The name of the secrets provider class to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
  # Provide the secrets, full path name, list.
  secrets: []

podAnnotations: {}

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: ""
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: 'worker_group=services'
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  # hosts:
  #   - host: chart-example.local
  #     paths:
  #       - path: /
  #         pathType: ImplementationSpecific
  # tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: "300m"
    memory: 512Mi
  requests:
    cpu: "100m"
    memory: 128Mi

pdb:
  enabled: false
  minAvailable: 1

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  version: autoscaling/v2
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
  behavior:
    scaledown:
      stabilizationWindowSeconds: 180  # 3 minutes
      policies:
        - type: Pods
          value: 1
          periodSeconds: 60
    scaleup:
      stabilizationWindowSeconds: 0
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 2
          periodSeconds: 15
      selectPolicy: Max

nodeSelector:
  worker_group: services

tolerations: []

affinity: {}
jobs: {}
