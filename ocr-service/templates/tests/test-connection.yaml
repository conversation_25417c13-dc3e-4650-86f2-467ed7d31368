apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "ocr-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "ocr-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "ocr-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
