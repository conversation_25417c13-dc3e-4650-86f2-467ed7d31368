name: Lint and Test Charts

on: 
  pull_request:
    branches: [master]

jobs:
  lint-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.8.1

      # Python is required because `ct lint` runs Yamale (https://github.com/23andMe/Yamale) and
      # yamllint (https://github.com/adrienverge/yamllint) which require Python
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.7

      - name: Set up chart-testing
        uses: helm/chart-testing-action@v2.2.1
        with:
          version: v3.5.1

      - name: Run chart-testing (list-changed)
        id: list-changed
        run: |
          changed=$(ct list-changed --config .github/utilities/ct.yaml)
          if [[ -n "$changed" ]]; then
            echo "::set-output name=changed::true"
          fi
      - name: Run chart-testing (lint)
        run: ct lint --config .github/utilities/ct.yaml

      # - name: Create kind cluster
      #   uses: helm/kind-action@v1.1.0
      #   with:
      #     version: v0.11.1
      #     cluster_name: we-deliver
      #     node_image: kindest/node:v1.21.1@sha256:69860bda5563ac81e3c0057d654b5253219618a22ec3a346306239bba8cfa1a6
      #     # log_level: debug
      #     kubectl_version: v1.21.7
      #     config: .github/utilities/cluster-config.yaml
      #   if: steps.list-changed.outputs.changed == 'true'

      # - name: Run chart-testing (install)
      #   run: ct install --config .github/utilities/ct.yaml
