name: Release Charts

on:
  push: 
    paths:
      - 'charts/**'
    branches:
      - master

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config user.name "$GITHUB_ACTOR"
          git config user.email "$<EMAIL>"
      - name: Install Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.4.1

#       - name: Run chart-releaser
#         uses: helm/chart-releaser-action@v1.2.1
#         with:
#           charts_dir: charts
#           charts_repo_url: https://raw.githubusercontent.com/WeDeliverGitHub/k8s_definitions/gh-pages/
#           # config: cr.yaml
#        env:
#          CR_TOKEN: "${{ secrets.K8S_DEF_GITHUB_ACCESS_TOKEN }}"
      - name: Publish Helm chart
        uses: stefanprodan/helm-gh-pages@master
        with:
          token: ${{ secrets.K8S_DEF_GITHUB_ACCESS_TOKEN }}
          charts_dir: charts
          charts_url: https://raw.githubusercontent.com/WeDeliverGitHub/k8s_definitions/gh-pages/
          owner: WeDeliverGitHub
          repository: k8s_definitions
          branch: gh-pages
          target_dir: charts
          index_dir: .
          commit_username: ${{ github.actor }}
          commit_email: "${{ github.actor }}@users.noreply.github.com"
