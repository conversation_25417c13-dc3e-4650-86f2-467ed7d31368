# This is a basic workflow to help you get started with Actions

name: CI Pipeline

on:
  push:
    paths-ignore:
      - '.github/**'
      - '**/README.md'
      - '.prettierignore'
      - '.gitignore'
      - 'charts/**'
    branches: [master]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  main:
    runs-on: ubuntu-latest
    # These permissions are needed to interact with GitHub's OIDC Token endpoint.
    permissions:
      contents: read
      actions: read

    steps:
      - name: New K8s Configurations Alert
        uses: 8398a7/action-slack@v3
        with:
          text: 'New K8s Configurations were pushed 🚀'
          status: 'success'
          author_name: 'Backend K8s Configuration'
          fields: repo,ref,message,author,eventName,workflow
          icon_emoji: ':rocket:'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_BACKEND_SRV_K8S_DEF_ALRT }}
        if: always()
