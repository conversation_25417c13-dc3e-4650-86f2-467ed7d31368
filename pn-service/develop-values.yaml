envName: development
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_pn_service:0b24b51e1df4561d1d472cd6175585389b97c811
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-pn-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: pn.services.wedeliver-dev.com
      paths:
        - path: /health_check
          pathType: Prefix
        - path: /api/v1/get_all_notifications
          pathType: Prefix
        - path: /api/v1/captain/notifications
          pathType: Prefix
        - path: /api/v1/captain/notifications/bell
          pathType: Prefix
        - path: /api/v1/captain/notifications/seen
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: develop/master-database/pn_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: develop/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: develop/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: develop/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: develop/website/fcm_api_key
      environmentVariables:
        - environmentVariableName: FCM_API_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: pn-service
environmentVariables:
  - name: FLASK_ENV
    value: development
  - name: FLASK_APP
    value: app.py
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: COUNTRY
    value: KSA
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: pushNotificationServiceDB
  - name: SITE_DOMAIN
    value: ''
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: AUTH_SERVICE
    value: http://auth-service.services
