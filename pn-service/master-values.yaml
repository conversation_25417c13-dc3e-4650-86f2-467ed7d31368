envName: production
pdb:
  enabled: true
autoscaling:
  maxReplicas: 5
  minReplicas: 2
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-pn-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: pn.services.wedeliverapp.com
      paths:
        - path: /health_check
          pathType: Prefix
        - path: /api/v1/get_all_notifications
          pathType: Prefix
        - path: /api/v1/captain/notifications
          pathType: Prefix
        - path: /api/v1/captain/notifications/bell
          pathType: Prefix
        - path: /api/v1/captain/notifications/seen
          pathType: Prefix
    - host: api.wedeliverapp.com
      paths:
        - path: /pn/health_check
          pathType: Prefix
        - path: /pn/api/v1/get_all_notifications
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/master-database/pn_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: production/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: production/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: production/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: production/website/fcm_api_key
      environmentVariables:
        - environmentVariableName: FCM_API_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: pn-service
environmentVariables:
  - name: FLASK_ENV
    value: production
  - name: FLASK_APP
    value: app.py
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: COUNTRY
    value: KSA
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: pushNotificationServiceDB
  - name: SITE_DOMAIN
    value: ''
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: AUTH_SERVICE
    value: http://auth-service.services
