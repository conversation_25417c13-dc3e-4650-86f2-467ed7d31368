apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "pn-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "pn-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "pn-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
