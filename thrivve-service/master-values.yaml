envName: production
pdb:
  enabled: true
autoscaling:
  maxReplicas: 10
  minReplicas: 2
databaseMigration:
  enabled: true
  activeDeadlineSeconds: 900
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-thrivve-service-sa-role
ingress:
  enabled: true
  className: ""
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: thrivve.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/master-database/thrivve_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: production/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: production/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: production/website/thrivve_fcm_api_key
      environmentVariables:
        - environmentVariableName: FCM_SERVICE_ACCOUNT_JSON
          secretKey: SELF
    - asmName: production/paymob/paymob_credentials
      environmentVariables:
        - environmentVariableName: PAYMOB_SECRET_API_KEY
          secretKey: secret_api_key
        - environmentVariableName: PAYMOB_HMAC_SECRET
          secretKey: hmac_secret
        - environmentVariableName: PAYMOB_DEFAULT_PAYMENT_METHOD
          secretKey: default_payment_method
        - environmentVariableName: PAYMOB_PUBLIC_KEY
          secretKey: public_key
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: thrivve-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: thrivveServiceDB
  - name: DATABASE_SERVER
    value: prod-db-proxy.proxy-cxuo2upvdyyc.eu-west-1.rds.amazonaws.com
  - name: DATABASE_READONLY
    value: prod-db-proxy.proxy-cxuo2upvdyyc.eu-west-1.rds.amazonaws.com
  - name: FLASK_ENV
    value: production
  - name: SENTRY_ENVIRONMENT
    value: production
  - name: SENTRY_ENABLED
    value: "True"
  - name: SENTRY_PROJECT_DSN
    value: https://<EMAIL>/4509300395999312
  - name: SENTRY_RELEASE
    value: ae075d4c7654c950a7836950f4c464ca1bf9b227
  - name: SENTRY_PROFILES_SAMPLE_RATE
    value: '0.01'
  - name: SQLALCHEMY_ECHO
    value: "False"
  - name: DEBUG
    value: "True"
  - name: FORCE_LOGOUT_CUSTOMER_APP_UTC_DATETIME
    value: "2024-07-01 09:42"
  - name: FLASK_APP
    value: app.py
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: "True"
  - name: SMS_SENDER_NAME
    value: "Thrivve"
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ALLOWED_ORIGIN
    value: "*"
  - name: S3_BUCKET
    value: thrivve-customers-assets
  - name: CUSTOMERS_ASSETS_URL
    value: https://thrivve-customers-assets.s3.eu-west-1.amazonaws.com
  - name: FRONT_END_WEBSITE_URL
    value: https://business.wedeliverapp.com
  - name: MICRO_FETCHER_INSTEAD_OF_KAFKA
    value: "False"
  - name: INVOICE_SERVICE
    value: http://invoice-service.services
  - name: TAMWHEEL_SERVICE
    value: http://tamwheel-service.services
  - name: UBER_ONBOARDING_WEBSITE_URL
    value: https://d3gtjdohtoeiug.cloudfront.net
  - name: UBER_REFERRAL_BASE_URL
    value: thrivve.me
  - name: PAYMOB_BASE_URL
    value: https://ksa.paymob.com
  - name: PAYMOB_NOTIFICATION_URL
    value: https://thrivve.services.wedeliverapp.com/payment/api/v1/paymob/notification
  - name: PAYMOB_REDIRECTION_URL
    value: https://ksa.paymob.com/api/acceptance/post_pay
  - name: ACCESS_TOKEN_EXPIRATION_MINUTES
    value: "60"
  - name: REFRESH_TOKEN_EXPIRATION_DAYS
    value: "30"
jobs:
  watch-expiry-customer-identity:
    suspend: false
  watch-bulk-transactions-queue:
    suspend: false
  watch-bulk-payments-queue:
    suspend: true
  watch-bulk-withdraw-req-queue:
    suspend: false
  notify-bulk-transactions-queue:
    suspend: false
  update-anb-payment-status:
    suspend: false
  add-customer-payment-monday:
    suspend: false
  add-customer-payment-daily:
    suspend: false
  delayed-customer-instant-payments:
    suspend: false
  track-gig-worker-negative-balance:
    suspend: false
  track-gig-worker-navi-balance-rh:
    suspend: false
  track-customer-referral-entries:
    suspend: false
  bulk-customer-uberdata-taskqueue:
    suspend: false
  update-customer-app-version:
    suspend: false
  uber-bulk-transactions-queue:
    suspend: false
  aggregate-customer-data:
    suspend: false
  execute-draft-uber-transaction:
    suspend: true
  auto-approve-uber-applications:
    suspend: false
  fetch-vehicle-catalog:
    suspend: false
  check-security-deposit-install:
    suspend: false
  alert-no-public-fleet:
    suspend: false
  process-loan-rules:
    suspend: false
  execute-bulk-actions:
    suspend: false
  calculate-customer-scores:
    suspend: false
