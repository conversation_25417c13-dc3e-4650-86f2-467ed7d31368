envName: staging
imageSource: "************.dkr.ecr.eu-west-1.amazonaws.com/backend_thrivve_service:b967a6c4b03806d0d4c9e1759cefff7a2b36d05e"
autoscaling:
  maxReplicas: 10
  minReplicas: 1
databaseMigration:
  enabled: true
  activeDeadlineSeconds: 300
livenessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 70
  timeoutSeconds: 20
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 5
readinessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 70
  timeoutSeconds: 20
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 5
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/staging-thrivve-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: thrivve.services.wedeliver-staging.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: staging/master-database/thrivve_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: staging/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: staging/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: staging/website/thrivve_fcm_api_key
      environmentVariables:
        - environmentVariableName: FCM_SERVICE_ACCOUNT_JSON
          secretKey: SELF
    - asmName: staging/paymob/paymob_credentials
      environmentVariables:
        - environmentVariableName: PAYMOB_SECRET_API_KEY
          secretKey: secret_api_key
        - environmentVariableName: PAYMOB_HMAC_SECRET
          secretKey: hmac_secret
        - environmentVariableName: PAYMOB_DEFAULT_PAYMENT_METHOD
          secretKey: default_payment_method
        - environmentVariableName: PAYMOB_PUBLIC_KEY
          secretKey: public_key
    - asmName: staging/adjust/adjust_credentials
      environmentVariables:
        - environmentVariableName: ADJUST_APP_TOKEN
          secretKey: app_token
    - asmName: staging/firebase/firebase_credentials
      environmentVariables:
        - environmentVariableName: FIREBASE_API_SECRET
          secretKey: api_secret
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: thrivve-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: thrivveServiceDB
  - name: DATABASE_SERVER
    value: master-database.default
  - name: DATABASE_READONLY
    value: master-database.default
  - name: FLASK_ENV
    value: staging
  - name: SENTRY_ENVIRONMENT
    value: staging
  - name: SENTRY_PROJECT_DSN
    value: https://<EMAIL>/4509300375748688
  - name: SENTRY_ENABLED
    value: 'True'
  - name: SENTRY_RELEASE
    value: "b967a6c4b03806d0d4c9e1759cefff7a2b36d05e"
  - name: SENTRY_PROFILES_SAMPLE_RATE
    value: '0.05'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'True'
  - name: FORCE_LOGOUT_CUSTOMER_APP_UTC_DATETIME
    value: 2024-06-27 14:15
  - name: FLASK_APP
    value: app.py
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: S3_BUCKET
    value: thrivve-customers-assets-staging
  - name: CUSTOMERS_ASSETS_URL
    value: https://thrivve-customers-assets-staging.s3.eu-west-1.amazonaws.com
  - name: FRONT_END_WEBSITE_URL
    value: https://business.wedeliver-staging.com
  - name: MICRO_FETCHER_INSTEAD_OF_KAFKA
    value: 'False'
  - name: INVOICE_SERVICE
    value: http://invoice-service.services
  - name: TAMWHEEL_SERVICE
    value: http://tamwheel-service.services
  - name: UBER_ONBOARDING_WEBSITE_URL
    value: https://uber1.wedeliver-staging.com
  - name: UBER_REFERRAL_BASE_URL
    value: wedeliver-staging.com
  - name: PAYMOB_BASE_URL
    value: https://ksa.paymob.com
  - name: PAYMOB_NOTIFICATION_URL
    value: https://thrivve.services.wedeliver-staging.com/payment/api/v1/paymob/notification
  - name: PAYMOB_REDIRECTION_URL
    value: https://ksa.paymob.com/api/acceptance/post_pay
  - name: ACCESS_TOKEN_EXPIRATION_MINUTES
    value: '15'
  - name: REFRESH_TOKEN_EXPIRATION_DAYS
    value: '1'
  - name: FIREBASE_MEASUREMENT_ID
    value: G-XXXXXXX
  - name: ADJUST_EVENT_TOKENS_ORDER_CONFIRMED
    value: apohqe
  - name: ADJUST_EVENT_TOKENS_ORDER_REJECTED
    value: 3uk8tf
  - name: ADJUST_EVENT_TOKENS_REFUND_COMPLETED
    value: qjw2eh
jobs:
  watch-expiry-customer-identity:
    suspend: true
  watch-bulk-transactions-queue:
    suspend: true
  watch-bulk-payments-queue:
    suspend: true
  watch-bulk-withdraw-req-queue:
    suspend: true
  notify-bulk-transactions-queue:
    suspend: true
  update-anb-payment-status:
    suspend: true
  delayed-customer-instant-payments:
    suspend: true
  track-gig-worker-negative-balance:
    suspend: true
  track-gig-worker-navi-balance-rh:
    suspend: true
  track-customer-referral-entries:
    suspend: true
  bulk-customer-uberdata-taskqueue:
    suspend: true
  aggregate-customer-data:
    suspend: true
  execute-draft-uber-transaction:
    suspend: true
  update-customer-app-version:
    suspend: true
  uber-bulk-transactions-queue:
    suspend: true
  auto-approve-uber-applications:
    suspend: true
  check-security-deposit-install:
    suspend: true
  alert-no-public-fleet:
    suspend: false
  process-loan-rules:
    suspend: false
  execute-bulk-actions:
    suspend: true
  calculate-customer-scores:
    suspend: true
