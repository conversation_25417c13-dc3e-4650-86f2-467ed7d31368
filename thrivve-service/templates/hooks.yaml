{{- $fullname := include "thrivve-service.fullname" . }}
{{- $serviceAccountName := include "thrivve-service.serviceAccountName" . }}
{{- $volumeMounts := include "thrivve-service.volumeMounts" .}}
{{- $volumes := include "thrivve-service.volumes" .}}
{{- $envs := include "thrivve-service.envs" . }}

{{- if .Values.databaseMigration.enabled }}
---
apiVersion: batch/v1
kind: Job
metadata:
  generateName: "{{ $fullname }}-db-"
  annotations:
    argocd.argoproj.io/hook: PreSync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
    argocd.argoproj.io/sync-options: "Timeout=900"
    argocd.argoproj.io/sync-wave: "1"
spec:
  backoffLimit: 2
  activeDeadlineSeconds: {{ default "300" $.Values.databaseMigration.activeDeadlineSeconds }}
  template:
    spec:
        serviceAccountName: {{ $serviceAccountName }}
        securityContext:
        {{- toYaml $.Values.podSecurityContext | nindent 12 }}
        containers:
          - image: {{ include "thrivve-service.imageSource" . | indent 8 }}
            imagePullPolicy: {{ $.Values.image.pullPolicy }}
            name: "{{ $fullname }}-db-migrate"
            env:
                {{- $envs | nindent 12 }}
            command: 
                - "/bin/sh"
            args:
                - "-c"
                - "export PYTHONPATH=. && python {{ $.Values.databaseMigration.scriptPath }}"
                # - "echo \"init...\n\" && python3 manage.py db init"
                # - "&& echo \"migrate...\n\" && python3 manage.py db migrate"
                # - "&& echo \"upgarde...\n\" && python3 manage.py db upgrade"
            resources:
                {{- toYaml $.Values.resources | nindent 14 }}
            volumeMounts:
                {{- $volumeMounts | nindent 12 }}
        {{- with $.Values.nodeSelector }}
        nodeSelector:
        {{- toYaml . | nindent 12 }}
        {{- end }}
        {{- with $.Values.affinity }}
        affinity:
        {{- toYaml . | nindent 12 }}
        {{- end }}
        {{- with $.Values.tolerations }}
        tolerations:
        {{- toYaml . | nindent 12 }}
        {{- end }}
        volumes:
        {{- $volumes | nindent 12 }}
        restartPolicy: Never
{{- end }}
