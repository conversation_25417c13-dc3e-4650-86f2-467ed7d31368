apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "thrivve-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "thrivve-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "thrivve-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
