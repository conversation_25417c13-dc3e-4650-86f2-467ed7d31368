team: backend-team
nameOverride: ''
fullnameOverride: ''
replicaCount: 1
revisionHistoryLimit: 10
maxUnavailable: 25%
maxSurge: 25%
progressDeadlineSeconds: 300
terminationGracePeriodSeconds: 30
image:
  repository: 026392744560.dkr.ecr.eu-west-1.amazonaws.com/backend_thrivve_service
  pullPolicy: IfNotPresent
  tag: 7740549ba8e92c8d79b87f82334b4357eef7bfd0
imagePullSecrets: []
databaseMigration:
  enabled: false
  scriptPath: db_sync.py
containerPorts:
  - containerPort: 8000
    name: http
    protocol: TCP
livenessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 60
  timeoutSeconds: 20
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 5
readinessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 60
  timeoutSeconds: 20
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 5
commonEnvironmentVariables:
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: SERVICE_NAME
    value: thrivve-service
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: CORE_SERVICE
    value: http://thrivve-service.services
  - name: AUTH_SERVICE
    value: http://thrivve-service.services
  - name: CONFIG_SERVICE
    value: http://thrivve-service.services
  - name: FINANCE_SERVICE
    value: http://thrivve-service.services
  - name: PAYMENT_SERVICE
    value: http://thrivve-service.services
  - name: NOTIFICATION_SERVICE
    value: http://thrivve-service.services
  - name: PRODUCT_SERVICE
    value: http://thrivve-service.services
  - name: ASSETS_SERVICE
    value: http://thrivve-service.services
  - name: ANB_SERVICE
    value: http://anb-service.services
  - name: TRANSLATE_SERVICES
    value: finance,core,auth,config,notification,integration,logs,sms,payment,assets,product
  - name: SENTRY_SAMPLE_RATE
    value: '1.0'
  - name: SENTRY_TRACES_SAMPLE_RATE
    value: '1.0'
  - name: SENTRY_PROFILES_SAMPLE_RATE
    value: '1.0'
  - name: SENTRY_SEND_DEFAULT_PII
    value: 'True'
volumeMounts: []
volumes: []
serviceAccount:
  create: false
  annotations: {}
  name: ''
secretProviderClass:
  version: secrets-store.csi.x-k8s.io/v1
  create: false
  annotations: {}
  name: ''
  secrets: []
podAnnotations: {}
podSecurityContext: {}
securityContext: {}
service:
  type: ClusterIP
  port: 80
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: worker_group=services
resources:
  limits:
    cpu: 500m
    memory: 2Gi
  requests:
    cpu: 300m
    memory: 1Gi
pdb:
  enabled: false
  minAvailable: 1
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  version: autoscaling/v2
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
  behavior:
    scaledown:
      stabilizationWindowSeconds: 180
      policies:
        - type: Pods
          value: 1
          periodSeconds: 60
      selectPolicy: Max
    scaleup:
      stabilizationWindowSeconds: 0
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 2
          periodSeconds: 15
      selectPolicy: Max
nodeSelector:
  worker_group: services
tolerations: []
affinity: {}
jobs:
  watch-expiry-customer-identity:
    suspend: true
    schedule: 12 0 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/core/expire_customer_identity.py
  watch-bulk-transactions-queue:
    suspend: true
    schedule: 1-56/5 * * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/watch_bulk_transactions_queue.py
  watch-bulk-payments-queue:
    suspend: true
    schedule: 2-57/5 * * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/watch_bulk_payments_queue.py
  watch-bulk-withdraw-req-queue:
    suspend: true
    schedule: 3-58/5 * * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/watch_bulk_withdraw_requests_queue.py
  notify-bulk-transactions-queue:
    suspend: true
    schedule: 4-59/5 * * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/notify_bulk_transactions_queue.py
  update-anb-payment-status:
    suspend: true
    schedule: '1-58/3 * * * *'
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/update_anb_payment_status.py
  add-customer-payment-monday:
    suspend: true
    schedule: 50 0 * * 1
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/add_customer_payment_monday.py
  add-customer-payment-daily:
    suspend: true
    schedule: 0 9 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/add_customer_payment_daily.py
  delayed-customer-instant-payments:
    suspend: true
    schedule: 7 */4 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/delayed_customer_instant_payments.py
  track-gig-worker-negative-balance:
    suspend: true
    schedule: 0 9 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/core/track_gig_worker_negative_balance.py
  track-gig-worker-navi-balance-rh:
    suspend: true
    schedule: 5 10 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/core/track_gig_worker_negative_balance_ride_hailing.py
  track-customer-referral-entries:
    suspend: true
    schedule: 5 9 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/core/track_customer_referral_entries.py
  bulk-customer-uberdata-taskqueue:
    suspend: true
    schedule: '0-55/5 * * * *'
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/core/bulk_customer_uber_data_task_queue_execute.py
  update-customer-app-version:
    suspend: true
    schedule: 0 4 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/config/get_app_version.py
  uber-bulk-transactions-queue:
    suspend: true
    schedule: '*/30 * * * *'
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/uber_bulk_transactions_queue.py
  aggregate-customer-data:
    suspend: true
    schedule: 0 */6 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/core/aggregate_customer_data.py
  execute-draft-uber-transaction:
    suspend: true
    schedule: '2-57/5 * * * *'
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/execute_draft_uber_transaction.py
  auto-approve-uber-applications:
    suspend: true
    schedule: '3-58/5 * * * *'
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/core/auto_approve_uber_applications.py
  fetch-vehicle-catalog:
    suspend: true
    schedule: 0 0 * * 0
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/assets/fetch_vehicle_catalog.py
  check-security-deposit-install:
    suspend: true
    schedule: 0 */6 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/handel_due_security_deposit_installments.py
  alert-no-public-fleet:
    suspend: true
    schedule: '5-50/15 * * * *'
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/assets/alert_no_public_fleet.py
  process-loan-rules:
    suspend: true
    schedule: 0 */6 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/loan_rules/process_loan_rules.py
  execute-bulk-actions:
    suspend: true
    schedule: '5-35/30 * * * *'
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/config/execute_bulk_actions.py
  calculate-customer-scores:
    suspend: true
    schedule: '0 0 * * 0'
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/core/calculate_customer_scores_cronjob.py
