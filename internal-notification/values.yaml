team: backend-team
nameOverride: ''
fullnameOverride: ''
replicaCount: 1
revisionHistoryLimit: 10
maxUnavailable: 25%
maxSurge: 25%
progressDeadlineSeconds: 60
terminationGracePeriodSeconds: 30
image:
  repository: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_internal_notification_service
  pullPolicy: IfNotPresent
  tag: 8d24129e1182dc788572b7f8966a20cb6abbc931
imagePullSecrets: []
containerPorts:
  - containerPort: 8000
    name: http
    protocol: TCP
commonEnvironmentVariables:
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: SERVICE_NAME
    value: internal
volumeMounts: []
volumes: []
serviceAccount:
  create: false
  annotations: {}
  name: ''
secretProviderClass:
  version: secrets-store.csi.x-k8s.io/v1
  create: false
  annotations: {}
  name: ''
  secrets: []
podAnnotations: {}
podSecurityContext: {}
securityContext: {}
service:
  type: ClusterIP
  port: 80
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: worker_group=services
resources:
  limits:
    cpu: 300m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
pdb:
  enabled: false
  minAvailable: 1
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  version: autoscaling/v2
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
  behavior:
    scaledown:
      stabilizationWindowSeconds: 180
      policies:
        - type: Pods
          value: 1
          periodSeconds: 60
    scaleup:
      stabilizationWindowSeconds: 0
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 2
          periodSeconds: 15
      selectPolicy: Max
nodeSelector:
  worker_group: services
tolerations: []
affinity: {}
jobs: {}
