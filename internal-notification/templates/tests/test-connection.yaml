apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "internal-notification.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "internal-notification.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "internal-notification.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
