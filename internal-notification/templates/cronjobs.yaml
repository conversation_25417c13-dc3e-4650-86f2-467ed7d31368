{{- $fullname := include "internal-notification.fullname" . }}
{{- $labels := include "internal-notification.labels" . }}
{{- $serviceAccountName := include "internal-notification.serviceAccountName" . }}
{{- $volumeMounts := include "internal-notification.volumeMounts" .}}
{{- $volumes := include "internal-notification.volumes" .}}
{{- $envs := include "internal-notification.envs" . }}
{{- $imageSource := include "internal-notification.imageSource" . }}

{{- range $key, $job := .Values.jobs }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: "{{ $fullname }}-{{ $key }}"
  labels:
    {{- $labels | nindent 4 }}
    cron: "{{ $fullname }}-{{ $key }}"
spec:
  suspend: {{ $job.suspend }}
  schedule: {{ $job.schedule | quote }}
  concurrencyPolicy: {{ default "Forbid" $job.concurrencyPolicy }}
  failedJobsHistoryLimit: {{ if kindIs "float64" $job.failedJobsHistoryLimit }}{{ $job.failedJobsHistoryLimit }}{{ else }}{{ 3 }}{{ end }}
  successfulJobsHistoryLimit: {{ if kindIs "float64" $job.successfulJobsHistoryLimit }}{{ $job.successfulJobsHistoryLimit }}{{ else }}{{ 1 }}{{ end }}
  jobTemplate:
    spec:
      backoffLimit: {{ default "2" $job.backoffLimit  }}
      activeDeadlineSeconds: {{ default "100" $job.activeDeadlineSeconds }}
      ttlSecondsAfterFinished: {{ default "300" $job.ttlSecondsAfterFinished }}
      template:
        metadata:
          {{- with $.Values.podAnnotations }}
          annotations:
              {{- toYaml . | nindent 12 }}
          {{- end }}
          labels:
            {{- $labels | nindent 12 }}
            job: "{{ $fullname }}-{{ $key }}"
        spec:
          serviceAccountName: {{ $serviceAccountName }}
          securityContext:
            {{- toYaml $.Values.podSecurityContext | nindent 12 }}
          containers:
          - image:{{ $imageSource | indent 8 }}
            imagePullPolicy: {{ $.Values.image.pullPolicy }}
            name: {{ $key }}
            env:
              {{- $envs | nindent 12 }}
            command: {{ default "[\"/bin/sh\"]" $job.command}}
            {{- with $job.args }}
            args:
              {{- toYaml . | nindent 12 }}
            {{- end }}
            resources:
            {{- if $job.resources }}
              {{- toYaml $job.resources | nindent 14 }}
            {{- else }}
              {{- toYaml $.Values.resources | nindent 14 }}
            {{- end }}
            volumeMounts:
              {{- $volumeMounts | nindent 12 }}
          {{- with $.Values.nodeSelector }}
          nodeSelector:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $.Values.affinity }}
          affinity:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $.Values.tolerations }}
          tolerations:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumes:
            {{- $volumes | nindent 12 }}
          restartPolicy: {{ default "OnFailure" $job.restartPolicy }}
{{- end }}
