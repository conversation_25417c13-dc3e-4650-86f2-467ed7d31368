imageSource: ''
autoscaling:
  maxReplicas: 3
service:
  type: ClusterIP
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-internal-notification-sa-role
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: internal.services.wedeliver-dev.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: develop/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: develop/internal-notification/slack_credentials
      environmentVariables:
        - environmentVariableName: SLACK_API_BOT_TOKEN
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: internal-notification
environmentVariables:
  - name: DEBUG
    value: 'False'
