autoscaling:
  maxReplicas: 2
  minReplicas: 1
service:
  type: ClusterIP
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-internal-notification-sa-role
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: sandbox-ingress
    alb.ingress.kubernetes.io/target-node-labels: worker_group=sandbox
  hosts:
    - host: internal.services.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: sandbox/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: sandbox/internal-notification/slack_credentials
      environmentVariables:
        - environmentVariableName: SLACK_API_BOT_TOKEN
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: internal-notification-sandbox
environmentVariables:
  - name: DEBUG
    value: 'False'
nodeSelector:
  worker_group: sandbox
