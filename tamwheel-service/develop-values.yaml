envName: development
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_tamwheel_service:7453b7a69e1c6d1ea4718bea88aa3280c94d254f
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-tamwheel-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: tamwheel.services.wedeliver-dev.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: develop/master-database/tamwheel_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: develop/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: develop/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: develop/website/tamwheel_fcm_api_key
      environmentVariables:
        - environmentVariableName: FCM_SERVICE_ACCOUNT_JSON
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: tamwheel-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: fintechDB
  - name: FLASK_ENV
    value: development
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: FLASK_APP
    value: app.py
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: DEBUG
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: S3_BUCKET
    value: tamwheel-assets-development
  - name: S3_ASSETS_URL
    value: https://tamwheel-assets-development.s3.eu-west-1.amazonaws.com
  - name: FRONT_END_WEBSITE_URL
    value: https://business.wedeliver-dev.com
  - name: MICRO_FETCHER_INSTEAD_OF_KAFKA
    value: 'False'
jobs:
  generate-vehicle-fines-invoices:
    suspend: true
  auto-generated-customer-invoice:
    suspend: true
  generate-preventive-maintenance:
    suspend: false
