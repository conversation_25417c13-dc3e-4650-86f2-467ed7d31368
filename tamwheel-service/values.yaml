team: backend-team
nameOverride: ''
fullnameOverride: ''
replicaCount: 1
revisionHistoryLimit: 10
maxUnavailable: 25%
maxSurge: 25%
progressDeadlineSeconds: 60
terminationGracePeriodSeconds: 30
image:
  repository: 026392744560.dkr.ecr.eu-west-1.amazonaws.com/backend_tamwheel_service
  pullPolicy: IfNotPresent
  tag: c92aaef89bfcb83755274db9f0ad740f39bff283
imagePullSecrets: []
enableDBMigration: false
containerPorts:
  - containerPort: 8000
    name: http
    protocol: TCP
livenessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 75
  timeoutSeconds: 15
  periodSeconds: 15
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 75
  timeoutSeconds: 15
  periodSeconds: 15
  successThreshold: 1
  failureThreshold: 3
commonEnvironmentVariables:
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: SERVICE_NAME
    value: tamwheel
  - name: DATABASE_SERVER
    value: master-database.default
  - name: DATABASE_READONLY
    value: read-database-replica.default
  - name: CORE_SERVICE
    value: http://tamwheel-service.services
  - name: AUTH_SERVICE
    value: http://tamwheel-service.services
  - name: CONFIG_SERVICE
    value: http://tamwheel-service.services
  - name: FINANCE_SERVICE
    value: http://tamwheel-service.services
  - name: NOTIFICATION_SERVICE
    value: http://tamwheel-service.services
  - name: LOG_SERVICE
    value: http://tamwheel-service.services
  - name: DELIVERY_INVOICE_SERVICE
    value: http://invoice-service.services
  - name: INVOICE_SERVICE
    value: http://tamwheel-service.services
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: MAINTENANCE_SERVICE
    value: http://tamwheel-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: TRANSLATE_SERVICES
    value: finance,core,auth,config,notification,integration,logs,sms,payment
volumeMounts: []
volumes: []
serviceAccount:
  create: false
  annotations: {}
  name: ''
secretProviderClass:
  version: secrets-store.csi.x-k8s.io/v1
  create: false
  annotations: {}
  name: ''
  secrets: []
podAnnotations: {}
podSecurityContext: {}
securityContext: {}
service:
  type: ClusterIP
  port: 80
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: worker_group=services
resources:
  limits:
    cpu: 300m
    memory: 1Gi
  requests:
    cpu: 100m
    memory: 900Mi
pdb:
  enabled: false
  minAvailable: 1
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  version: autoscaling/v2
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
  behavior:
    scaledown:
      stabilizationWindowSeconds: 180
      policies:
        - type: Pods
          value: 1
          periodSeconds: 60
    scaleup:
      stabilizationWindowSeconds: 0
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 2
          periodSeconds: 15
      selectPolicy: Max
nodeSelector:
  worker_group: services
tolerations: []
affinity: {}
jobs:
  generate-vehicle-fines-invoices:
    suspend: true
    schedule: 14 0 * * 0
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/generate_vehicle_fines_memo_invoice_cronjob.py
  auto-generated-customer-invoice:
    suspend: true
    schedule: 5 3 1 * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/finance/generate_customer_invoice_cronjob.py
  generate-preventive-maintenance:
    suspend: true
    schedule: 0 3 * * *
    args:
      - '-c'
      - export PYTHONPATH=. && python app/cronjobs/maintenance/generate_preventive_maintenance_cronjob.py
