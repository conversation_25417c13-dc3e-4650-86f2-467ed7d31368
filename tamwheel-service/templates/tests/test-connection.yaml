apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "tamwheel-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "tamwheel-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "tamwheel-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
