team: backend-team
nameOverride: ''
fullnameOverride: ''
replicaCount: 1
revisionHistoryLimit: 10
maxUnavailable: 25%
maxSurge: 25%
progressDeadlineSeconds: 60
terminationGracePeriodSeconds: 30
image:
  repository: 026392744560.dkr.ecr.eu-west-1.amazonaws.com/backend_sdd_service
  pullPolicy: IfNotPresent
  tag: 829efcaae8c1cf0e908bede7c5b2ecab671a23a7
imagePullSecrets: []
enableDBMigration: false
containerPorts:
  - containerPort: 8000
    name: http
    protocol: TCP
livenessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 30
  timeoutSeconds: 15
  periodSeconds: 15
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  httpGet:
    path: /health_check
    port: http
    scheme: HTTP
  initialDelaySeconds: 30
  timeoutSeconds: 15
  periodSeconds: 15
  successThreshold: 1
  failureThreshold: 3
commonEnvironmentVariables:
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: SERVICE_NAME
    value: sdd
  - name: DATABASE_SERVER
    value: master-database.default
  - name: DATABASE_READONLY
    value: read-database-replica.default
  - name: QUICKBOOKS_QBO_BASEURL
    value: https://quickbook-server.default
  - name: COUNTRY_CODE
    value: sa
  - name: METABASE_SERVER_URL
    value: https://metabase.services.wedeliverapp.com
volumeMounts: []
volumes: []
serviceAccount:
  create: false
  annotations: {}
  name: ''
secretProviderClass:
  version: secrets-store.csi.x-k8s.io/v1
  create: false
  annotations: {}
  name: ''
  secrets: []
podAnnotations: {}
podSecurityContext: {}
securityContext: {}
service:
  type: ClusterIP
  port: 80
ingress:
  enabled: false
  className: ''
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: worker_group=services
resources:
  limits:
    cpu: 300m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
pdb:
  enabled: false
  minAvailable: 1
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  version: autoscaling/v2
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
  behavior:
    scaledown:
      stabilizationWindowSeconds: 180
      policies:
        - type: Pods
          value: 1
          periodSeconds: 60
    scaleup:
      stabilizationWindowSeconds: 0
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 2
          periodSeconds: 15
      selectPolicy: Max
nodeSelector:
  worker_group: services
tolerations: []
affinity: {}
jobs:
  sync-order-with-tookan:
    suspend: true
    schedule: '*/2 * * * *'
    args:
      - '-c'
      - python sync_order_with_tookan.py
  sync-orders-with-thirdparty:
    suspend: true
    schedule: 7,37 * * * *
    args:
      - '-c'
      - python sync_orders_with_thirdparty.py
  unassigned-trips-report:
    suspend: false
    schedule: 5 5 * * *
    args:
      - '-c'
      - python unassigned_trips_report.py
  uncompleted-trips-report:
    suspend: false
    schedule: 10 5 * * *
    args:
      - '-c'
      - python uncompleted_trips_report.py
  unreviewed-trips-report:
    suspend: false
    schedule: 15 5 * * *
    args:
      - '-c'
      - python unreviewed_trips_report.py
  bt-check-confirmed-batches:
    suspend: false
    schedule: 15-55/5 * * * *
    args:
      - '-c'
      - python bt_check_confirm_batches.py
  bt-sync-batches-status:
    suspend: false
    schedule: 20-55/5 * * * *
    args:
      - '-c'
      - python bt_sync_batch_status.py
  bt-create-sync-batches-trips:
    suspend: false
    schedule: 25-55/5 * * * *
    args:
      - '-c'
      - python bt_sync_batch_trips.py
  create-captain-adjustment-transaction:
    suspend: false
    schedule: 12 0 1 * *
    args:
      - '-c'
      - export PYTHONPATH=. && python business_logic/cronjobs/calculate_captain_target_hungerstation.py
