apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sdd-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "sdd-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "sdd-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
