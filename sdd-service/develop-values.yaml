envName: development
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_sdd_service:931a66d40ad44312733b8c57dd8ae08c841df4d3
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-sdd-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: sdd.services.wedeliver-dev.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: develop/master-database/sdd_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: develop/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: develop/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: develop/tookan/tookan_credentials
      environmentVariables:
        - environmentVariableName: TOOKAN_SHARED_SECRET_KEY
          secretKey: shared_secret_key
        - environmentVariableName: TOOKAN_API_KEY
          secretKey: api_key
        - environmentVariableName: METABASE_SERVER_TOKEN
          secretKey: metabase_token
    - asmName: develop/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: develop/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: develop/website/fcm_api_key
      environmentVariables:
        - environmentVariableName: FCM_API_KEY
          secretKey: SELF
    - asmName: develop/parse/parse_secret_configurations
      environmentVariables:
        - environmentVariableName: PARSE_SERVER_REST_API_KEY
          secretKey: restAPIKey
    - asmName: develop/patch/patch_credential
      environmentVariables:
        - environmentVariableName: PATCH_USER_NAME
          secretKey: username
        - environmentVariableName: PATCH_PASSWORD
          secretKey: password
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: sdd-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: wedeliver_2
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: FLASK_ENV
    value: development
  - name: FLASK_APP
    value: app.py
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: OLD_SYSTEM_BASE_URL
    value: staging.wedeliverapp.com/
  - name: TOOKAN_BASE_URL
    value: https://api.tookanapp.com
  - name: SITE_DOMAIN
    value: 127.0.0.1:5000
  - name: SLACK_API_TOKEN
    value: ''
  - name: S3_REGION
    value: eu-west-1
  - name: S3_BUCKET
    value: dev-wedeliver-slips
  - name: USE_IN_SERVER_IMAGES
    value: 'False'
  - name: PROJECT_BASE_URL
    value: ''
  - name: PROJECT_IMAGES_PATH
    value: static/images
  - name: BACKEND_WEBSITE_URL
    value: https://sdd.services.wedeliver-dev.com
  - name: AMAZON_SLIP_URL
    value: https://dev-wedeliver-slips.s3-eu-west-1.amazonaws.com/
  - name: FRONT_END_WEBSITE_URL_FUFI
    value: https://business.wedeliver-dev.com
  - name: FRONT_END_WEBSITE_URL
    value: https://business.wedeliver-dev.com
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: THIRDPARTY_SERVICE_URL
    value: http://the3pl-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: FINANCE_SERVICE
    value: http://finance-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: SUPPLIER_SERVICE
    value: http://supplier-service.services
  - name: DEBUG
    value: 'False'
  - name: PARSE_BASE_URL
    value: https://parse-server.services.wedeliver-dev.com/parse
  - name: PARSE_APP_ID
    value: wedeliver-tracker
jobs:
  sync-order-with-tookan:
    suspend: true
  sync-orders-with-thirdparty:
    suspend: true
  unassigned-trips-report:
    suspend: true
  uncompleted-trips-report:
    suspend: true
  unreviewed-trips-report:
    suspend: true
  bt-check-confirmed-batches:
    suspend: true
    schedule: '*/5 * * * *'
  bt-sync-batches-status:
    suspend: true
    schedule: '*/5 * * * *'
  bt-create-sync-batches-trips:
    suspend: true
    schedule: '*/5 * * * *'
  create-captain-adjustment-transaction:
    suspend: true
