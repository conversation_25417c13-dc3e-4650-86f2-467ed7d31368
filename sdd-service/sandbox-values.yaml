envName: sandbox
autoscaling:
  maxReplicas: 2
  minReplicas: 1
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-sdd-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: sandbox-ingress
    alb.ingress.kubernetes.io/target-node-labels: worker_group=sandbox
  hosts:
    - host: sdd.services.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
commonEnvironmentVariables:
  - name: SERVICE_NAME
    value: sdd
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092
  - name: DATABASE_SERVER
    value: master-database.sandbox
  - name: DATABASE_READONLY
    value: read-database-replica.sandbox
  - name: QUICKBOOKS_QBO_BASEURL
    value: https://quickbook-server.sandbox
  - name: COUNTRY_CODE
    value: sa
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: sandbox/master-database/sdd_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: sandbox/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: sandbox/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: sandbox/tookan/tookan_credentials
      environmentVariables:
        - environmentVariableName: TOOKAN_SHARED_SECRET_KEY
          secretKey: shared_secret_key
        - environmentVariableName: TOOKAN_API_KEY
          secretKey: api_key
        - environmentVariableName: METABASE_SERVER_TOKEN
          secretKey: metabase_token
    - asmName: sandbox/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: sandbox/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: sandbox/website/fcm_api_key
      environmentVariables:
        - environmentVariableName: FCM_API_KEY
          secretKey: SELF
    - asmName: sandbox/patch/patch_credential
      environmentVariables:
        - environmentVariableName: PATCH_USER_NAME
          secretKey: username
        - environmentVariableName: PATCH_PASSWORD
          secretKey: password
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: sdd-service-sandbox
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: sddDB
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: FLASK_ENV
    value: sandbox
  - name: FLASK_APP
    value: app.py
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: OLD_SYSTEM_BASE_URL
    value: wedeliverapp.com/
  - name: TOOKAN_BASE_URL
    value: https://api.tookanapp.com
  - name: SITE_DOMAIN
    value: 127.0.0.1:5000
  - name: SLACK_API_TOKEN
    value: ''
  - name: S3_REGION
    value: eu-west-1
  - name: S3_BUCKET
    value: wedeliver-backend-assets
  - name: USE_IN_SERVER_IMAGES
    value: 'False'
  - name: PROJECT_BASE_URL
    value: ''
  - name: PROJECT_IMAGES_PATH
    value: static/images
  - name: BACKEND_WEBSITE_URL
    value: https://sdd.services.wedeliverapp.dev
  - name: AMAZON_SLIP_URL
    value: https://wedeliver-backend-assets.s3-eu-west-1.amazonaws.com/
  - name: FRONT_END_WEBSITE_URL_FUFI
    value: https://business.wedeliverapp.dev
  - name: FRONT_END_WEBSITE_URL
    value: https://business.wedeliverapp.dev
  - name: AUTH_SERVICE
    value: http://auth-service-sandbox.sandbox
  - name: THIRDPARTY_SERVICE_URL
    value: http://the3pl-service-sandbox.sandbox
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service-sandbox.sandbox
  - name: FINANCE_SERVICE
    value: http://finance-service-sandbox.sandbox
  - name: CAPTAIN_SERVICE
    value: http://captain-service-sandbox.sandbox
  - name: SUPPLIER_SERVICE
    value: http://supplier-service-sandbox.sandbox
  - name: DEBUG
    value: 'False'
jobs:
  sync-order-with-tookan:
    suspend: true
  sync-orders-with-thirdparty:
    suspend: true
  unassigned-trips-report:
    suspend: true
  uncompleted-trips-report:
    suspend: true
  unreviewed-trips-report:
    suspend: true
  bt-check-confirmed-batches:
    suspend: true
  bt-sync-batches-status:
    suspend: true
  bt-create-sync-batches-trips:
    suspend: true
nodeSelector:
  worker_group: sandbox
