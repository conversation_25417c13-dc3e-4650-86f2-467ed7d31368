apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "address-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "address-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "address-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
