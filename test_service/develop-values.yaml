image:
  tag: b44d1224d37c08c45972ef97e6bbc1ddfc061cb7
autoscaling:
  maxReplicas: 3
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-finance-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: finance1.services.wedeliver-dev.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: develop/master-database/finance_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: develop/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: develop/website/telegram_credentials
      environmentVariables:
        - environmentVariableName: TELEGRAM_CRITICAL_ERROR_BOT
          secretKey: CRITICAL_ERROR_BOT
        - environmentVariableName: TELEGRAM_CRITICAL_ERROR_GROUP
          secretKey: CRITICAL_ERROR_GROUP
    - asmName: develop/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: develop/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: develop/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: finance-service1
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: financeServiceDB
  - name: FLASK_ENV
    value: development
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: QUICKBOOKS_REDIRECT_URI
    value: https://finance.services.wedeliver-dev.com/callback2
  - name: QUICKBOOKS_ENV
    value: sandbox
  - name: QUICKBOOKS_REALMID
    value: '4620816365207117020'
