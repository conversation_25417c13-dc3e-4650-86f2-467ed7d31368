apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sms-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "sms-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "sms-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
