envName: development
imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_sms_service:8e8678df1d2ebb86d323ff4468bf3c745c793eb0
autoscaling:
  maxReplicas: 3
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/develop-sms-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: sms.services.wedeliver-dev.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: develop/master-database/sms_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: develop/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: develop/sms_gateway/epush_credentials
      environmentVariables:
        - environmentVariableName: EPUSH_USER_NAME
          secretKey: username
        - environmentVariableName: EPUSH_PASSWORD
          secretKey: password
    - asmName: develop/sms_gateway/hadara_credentials
      environmentVariables:
        - environmentVariableName: HADARA_API_KEY
          secretKey: api_key
    - asmName: develop/sms_gateway/unifonic_credentials
      environmentVariables:
        - environmentVariableName: UNIFONIC_APPSID
          secretKey: app_id
        - environmentVariableName: UNIFONIC_AUTH_TOKEN
          secretKey: auth_token
    - asmName: develop/sms_gateway/tweet_credentials
      environmentVariables:
        - environmentVariableName: TWEET_API_KEY
          secretKey: API_KEY
    - asmName: develop/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: sms-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: smsServiceDB
  - name: FLASK_ENV
    value: development
  - name: FLASK_APP
    value: app.py
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: COUNTRY
    value: KSA
  - name: HADARA_BASE_URL
    value: http://smsservice.hadara.ps:4545/SMS.ashx/bulkservice/sessionvalue
  - name: UNIFONIC_BASE_URL
    value: http://basic.unifonic.com/rest/SMS/messages
  - name: UNIFONIC_SENDERID
    value: We-Deliver
  - name: EPUSH_BASE_URL
    value: http://epusheg.com/api/api.php
  - name: EPUSH_SENDER_NAME
    value: Wedeliver
  - name: SMS_SERVICE
    value: UNIFONIC
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: TWEET_SENDER
    value: WeDeliver
  - name: TWEET_BASE_URL
    value: http://hotsms.ps/sendbulksms.php
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: CAPTAIN_SERVICE
    value: http://captain-service.services
