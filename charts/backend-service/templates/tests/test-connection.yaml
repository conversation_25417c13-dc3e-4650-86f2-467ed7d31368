apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "backend-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "backend-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "backend-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
