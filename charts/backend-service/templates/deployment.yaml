apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "backend-service.fullname" . }}
  labels:
    {{- include "backend-service.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  progressDeadlineSeconds: {{ .Values.progressDeadlineSeconds }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "backend-service.selectorLabels" . | nindent 6 }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: {{ .Values.maxUnavailable | default 1 }}
      maxSurge: {{ .Values.maxSurge | default 2 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "backend-service.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "backend-service.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            {{- toYaml .Values.commonEnvironmentVariables | nindent 12 }}
            {{- if .Values.environmentVariables -}}
            {{- toYaml .Values.environmentVariables | nindent 12 }}
            {{- end }}
            {{- if and (.Values.secretProviderClass.create) (.Values.secretProviderClass.createEnvironmentVariables) }}
            {{- $k8s_secret_name := printf "%s-asm-secrets" (include "backend-service.serviceAccountName" .) }}
            {{- range $secret := .Values.secretProviderClass.secrets }} 
            {{- range $env := $secret.environmentVariables }} 
            - name: {{ $env.environmentVariableName }}
              valueFrom:
                secretKeyRef:
                  name: {{ $k8s_secret_name }}
                  key: {{ $env.environmentVariableName }}
            {{- end }}
            {{- end }}
            {{- end }}
          lifecycle:
            {{- toYaml .Values.lifecyclePolicy | nindent 12 }}
          ports:
            {{- toYaml .Values.containerPorts | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            {{- include "backend-service.volumeMounts" . | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        {{- include "backend-service.volumes" . | nindent 8 }}
      restartPolicy: Always
      schedulerName: default-scheduler
