{{/*
Expand the name of the chart.
*/}}
{{- define "backend-service.name" -}}
{{- .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "backend-service.fullname" -}}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}
# {{- if .Values.fullnameOverride }}
# {{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
# {{- else }}
# {{- $name := default .Chart.Name .Values.nameOverride }}
# {{- if contains $name .Release.Name }}
# {{- .Release.Name | trunc 63 | trimSuffix "-" }}
# {{- else }}
# {{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
# {{- end }}
# {{- end }}


{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "backend-service.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "backend-service.labels" -}}
helm.sh/chart: {{ include "backend-service.chart" . }}
{{ include "backend-service.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "backend-service.selectorLabels" -}}
app.kubernetes.io/name: {{ include "backend-service.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app: {{ include "backend-service.name" . }}
team: {{ .Values.team }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "backend-service.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "backend-service.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the name of the secrets provider class to use
*/}}
{{- define "backend-service.secretsProviderClassName" -}}
{{- if .Values.secretProviderClass.create }}
{{- default (include "backend-service.fullname" .) .Values.secretProviderClass.name }}
{{- else }}
{{- default "default" .Values.secretProviderClass.name }}
{{- end }}
{{- end }}

{{/*
Conatiner volume mounts
*/}}
{{- define "backend-service.volumeMounts" -}}
{{- if .Values.secretProviderClass.create }}
{{- if .Values.secretProviderClass.volumeMounts }}
{{ toYaml .Values.secretProviderClass.volumeMounts }}
{{- end }}
{{- end }}
{{- if .Values.volumeMounts }}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}

{{/*
Volumes
*/}}
{{- define "backend-service.volumes" -}}
{{- if .Values.secretProviderClass.create }}
{{- if .Values.secretProviderClass.volumes }}
{{ toYaml .Values.secretProviderClass.volumes }}
{{- end }}
{{- end }}
{{- if .Values.volumeMounts -}}
{{- toYaml .Values.volumeMounts }}
{{- end }}
{{- end }}
