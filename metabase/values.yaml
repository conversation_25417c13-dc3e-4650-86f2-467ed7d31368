# Default values for metabase.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

team: platform-team

nameOverride: ""
fullnameOverride: ""

replicaCount: 1
revisionHistoryLimit: 10

maxUnavailable: 25%
maxSurge: 25%

progressDeadlineSeconds: 1200
terminationGracePeriodSeconds: 60

image:
  repository: metabase/metabase
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  # tag: v0.43.7.3
  tag: v0.51.1.3

imagePullSecrets: []

containerPorts:
  - containerPort: 3000
    name: http
    protocol: TCP

# livenessProbe:
#  httpGet:
#    path: /
#    port: http
#    scheme: HTTP
#  initialDelaySeconds: 120
#  timeoutSeconds: 30
#  periodSeconds: 10
#  successThreshold: 1
#  failureThreshold: 6
# readinessProbe:
#  httpGet:
#    path: /
#    port: http
#    scheme: HTTP
#  initialDelaySeconds: 30
#  timeoutSeconds: 5
#  periodSeconds: 5
#  successThreshold: 1
#  failureThreshold: 3

commonEnvironmentVariables:
  - name: MB_DB_TYPE
    value: "mysql"
  - name: MB_PASSWORD_COMPLEXITY
    value: "normal"
  - name: MB_PASSWORD_LENGTH
    value: "8"
  - name: JAVA_TIMEZONE
    value: "Asia/Riyadh"
  - name: MB_JETTY_HOST
    value: "0.0.0.0"

environmentVariables: []

volumeMounts: []
volumes: []

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

secretProviderClass:
  version: secrets-store.csi.x-k8s.io/v1
  # Specifies whether a secrets provider class should be created
  create: true
  # Annotations to add to the secrets provider class
  annotations: {}
  # The name of the secrets provider class to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
  # Provide the secrets, full path name, list.
  secrets: []

podAnnotations: {}

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  type: NodePort
  port: 80

ingress:
  enabled: true
  className: ""
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/target-node-labels: 'worker_group=services'

resources:
  limits:
    cpu: "1000m"
    memory: 1512Mi
  requests:
    cpu: "500m"
    memory: 1024Mi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  version: autoscaling/v2
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 75
  behavior:
    scaledown:
      stabilizationWindowSeconds: 180  # 3 minutes
      policies:
        - type: Pods
          value: 1
          periodSeconds: 60
    scaleup:
      stabilizationWindowSeconds: 0
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 2
          periodSeconds: 15
      selectPolicy: Max

nodeSelector:
  worker_group: services

tolerations: []

affinity: {}
