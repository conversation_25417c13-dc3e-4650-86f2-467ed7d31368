apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "metabase.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "metabase.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "metabase.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
