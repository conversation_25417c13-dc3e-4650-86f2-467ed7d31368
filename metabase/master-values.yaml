serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-metabase-sa-role

ingress:
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: metabase.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix

secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/metabase/metabase_database_credentials
      environmentVariables:
        - environmentVariableName: MB_DB_CONNECTION_URI
          secretKey: connection_uri
    - asmName: production/internal-notification/slack_credentials
      environmentVariables:
        - environmentVariableName: SLACK_API_BOT_TOKEN
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: metabase
