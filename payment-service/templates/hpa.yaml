{{- if .Values.autoscaling.enabled }}
apiVersion: {{ .Values.autoscaling.version }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "payment-service.fullname" . }}
  labels:
    {{- include "payment-service.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "payment-service.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  {{- if .Values.autoscaling.metrics }}
  metrics:
    {{- toYaml .Values.autoscaling.metrics | nindent 4 }}
  {{- end }}
  {{- if .Values.autoscaling.behavior }}
  behavior:
    scaleDown:
      {{- toYaml .Values.autoscaling.behavior.scaledown | nindent 6 }}
    scaleUp:
      {{- toYaml .Values.autoscaling.behavior.scaleup | nindent 6 }}
  {{- end }}
{{- end }}
