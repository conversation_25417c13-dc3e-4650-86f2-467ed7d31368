apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "payment-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "payment-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "payment-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
