envName: sandbox
autoscaling:
  maxReplicas: 2
  minReplicas: 1
enableDBMigration: true
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/sandbox-fintech-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: sandbox-ingress
    alb.ingress.kubernetes.io/target-node-labels: worker_group=sandbox
  hosts:
    - host: fintech.services.wedeliverapp.dev
      paths:
        - path: /
          pathType: Prefix
commonEnvironmentVariables:
  - name: SERVICE_NAME
    value: fintech
  - name: BOOTSTRAP_SERVERS
    value: pkc-e8mp5.eu-west-1.aws.confluent.cloud:9092    
  - name: DATABASE_SERVER
    value: master-database.sandbox
  - name: DATABASE_READONLY
    value: read-database-replica.sandbox

secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: sandbox/master-database/fintech_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: sandbox/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: sandbox/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF 
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: fintech-service-sandbox
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: fintechDB
  - name: FLASK_ENV
    value: sandbox
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: FLASK_APP
    value: app.py
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'True'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: AUTH_SERVICE
    value: http://auth-service-sandbox.sandbox
  - name: FINANCE_SERVICE
    value: http://finance-service-sandbox.sandbox
  - name: SUPPLIER_SERVICE
    value: http://supplier-service-sandbox.sandbox
  - name: SDD_SERVICE
    value: http://sdd-service-sandbox.sandbox
  - name: PN_SERVICE
    value: http://pn-service-sandbox.sandbox
  - name: CAPTAIN_SERVICE
    value: http://captain-service-sandbox.sandbox
  - name: WEDELIVER_SUPPLIER_ID
    value: '1'
  - name: FRONT_END_WEBSITE_URL
    value: https://business.wedeliverapp.dev
nodeSelector:
  worker_group: sandbox
jobs:
  over-date-installments:
    suspend: true
    schedule: '0 0 * * *'
  alert-expired-docs-dates:
    suspend: false
