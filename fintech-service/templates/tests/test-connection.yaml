apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "fintech-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "fintech-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "fintech-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
