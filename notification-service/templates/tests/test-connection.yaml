apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "notification-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "notification-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "notification-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
