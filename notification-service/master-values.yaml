imageSource: ************.dkr.ecr.eu-west-1.amazonaws.com/backend_notification_service:5bac4a99817b6b24e87e591a511037de240e4c64
autoscaling:
  maxReplicas: 3
service:
  type: NodePort
  port: 80
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-notification-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: notification.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/master-database/notification_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: production/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: production/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: production/mail/sendgrid_credentials
      environmentVariables:
        - environmentVariableName: SENDGRID_API_KEY
          secretKey: SELF
    - asmName: production/internal-notification/slack_credentials
      environmentVariables:
        - environmentVariableName: SLACK_API_BOT_TOKEN
          secretKey: SELF
    - asmName: production/website/notification_fcm_api_key
      environmentVariables:
        - environmentVariableName: FCM_SERVICE_ACCOUNT_JSON
          secretKey: SELF
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: notification-service
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: notificationServiceDB
  - name: FLASK_ENV
    value: production
  - name: FLASK_APP
    value: app.py
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'False'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: SESSION_COOKIE_NAME
    value: session
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'True'
  - name: CSRF_ENABLED
    value: 'True'
  - name: COUNTRY
    value: KSA
  - name: SENDER_EMAIL
    value: <EMAIL>
  - name: SES_REGION_NAME
    value: eu-west-1
  - name: SENDER_NAME
    value: Thrivve
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
