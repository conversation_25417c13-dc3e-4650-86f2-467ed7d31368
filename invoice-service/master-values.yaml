envName: production
pdb:
  enabled: true
autoscaling:
  maxReplicas: 5
  minReplicas: 2
enableDBMigration: true
service:
  type: NodePort
  port: 80
zatcaSdk:
  tag: 3.3.3-1
serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-invoice-service-sa-role
ingress:
  enabled: true
  className: ''
  annotations:
    alb.ingress.kubernetes.io/group.name: services-ingress
  hosts:
    - host: invoice.services.wedeliverapp.com
      paths:
        - path: /
          pathType: Prefix
    - host: api.wedeliverapp.com
      paths:
        - path: /sa/invoice/
          pathType: Prefix
        - path: /invoice/
          pathType: Prefix
secretProviderClass:
  create: true
  createEnvironmentVariables: true
  secrets:
    - asmName: production/master-database/invoice_database_credentials
      environmentVariables:
        - environmentVariableName: DATABASE_USERNAME
          secretKey: username
        - environmentVariableName: DATABASE_PASSWORD
          secretKey: password
    - asmName: production/sasl/sasl_credentials
      environmentVariables:
        - environmentVariableName: SASL_USERNAME
          secretKey: USERNAME
        - environmentVariableName: SASL_PASSWORD
          secretKey: PASSWORD
    - asmName: production/zatca/cleartax_credentials
      environmentVariables:
        - environmentVariableName: CLEARTAX_API_KEY
          secretKey: api_key
        - environmentVariableName: CLEARTAX_DEVICE_ID
          secretKey: device_id_wedeliver
        - environmentVariableName: CLEARTAX_VAT_NUMBER
          secretKey: vat_number_wedeliver
        - environmentVariableName: CLEARTAX_DEVICE_ID_LAST_MILE
          secretKey: device_id_last_mile
        - environmentVariableName: CLEARTAX_VAT_NUMBER_LAST_MILE
          secretKey: vat_number_last_mile
    - asmName: production/quickbooks/quickbooks_credentials
      environmentVariables:
        - environmentVariableName: QUICKBOOKS_CLIENT_SECRET
          secretKey: CLIENT_SECRET
        - environmentVariableName: QUICKBOOKS_CLIENT_ID
          secretKey: CLIENT_ID
    - asmName: production/website/google_api_key
      environmentVariables:
        - environmentVariableName: GOOGLE_API_KEY
          secretKey: SELF
    - asmName: production/website/secret_key
      environmentVariables:
        - environmentVariableName: SECRET_KEY
          secretKey: SELF
    - asmName: production/zatca/zatca_config_keys
      environmentVariables:
        - environmentVariableName: ZATCA_PRIVATE_KEY
          secretKey: private_key
        - environmentVariableName: ZATCA_CERTIFICATE
          secretKey: certificate
  volumeMounts:
    - name: aws-secrets
      mountPath: /etc/sm
      readOnly: true
    - name: zatca-sdk-data
      mountPath: /app/zatca-sdk-238-R3.3.3
    - name: zatca-java-data
      mountPath: /shared-libraries/openjdk-11
  volumes:
    - name: aws-secrets
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: invoice-service
    - name: zatca-sdk-data
      emptyDir: {}
    - name: zatca-java-data
      emptyDir: {}
environmentVariables:
  - name: DATABASE_ENGINE
    value: mysql
  - name: DATABASE_NAME
    value: invoiceServiceDB
  - name: SQLALCHEMY_TRACK_MODIFICATIONS
    value: 'False'
  - name: TESTING
    value: 'False'
  - name: ALLOWED_ORIGIN
    value: '*'
  - name: FLASK_ENV
    value: production
  - name: SQLALCHEMY_ECHO
    value: 'False'
  - name: DEBUG
    value: 'True'
  - name: OAUTHLIB_INSECURE_TRANSPORT
    value: 'True'
  - name: PROPAGATE_EXCEPTIONS
    value: 'True'
  - name: PRESERVE_CONTEXT_ON_EXCEPTION
    value: 'True'
  - name: TRAP_HTTP_EXCEPTIONS
    value: 'False'
  - name: TRAP_BAD_REQUEST_ERRORS
    value: 'False'
  - name: CLEARTAX_ENABLED
    value: 'True'
  - name: CLEARTAX_BASE_URL
    value: https://api.cleartax.com/middle-east/ksa/einvoicing
  - name: SESSION_COOKIE_NAME
    value: session
  - name: COUNTRY
    value: KSA
  - name: CURRENCY
    value: SAR
  - name: CSRF_ENABLED
    value: 'True'
  - name: ENABLE_TELEGRAM_ALERTS
    value: 'True'
  - name: BACKEND_WEBSITE_URL
    value: ''
  - name: SDD_SERVICE
    value: http://sdd-service.services
  - name: AUTH_SERVICE
    value: http://auth-service.services
  - name: PUBLIC_SERVICE
    value: http://public-service.services
  - name: FINANCE_SERVICE
    value: http://finance-service.services
  - name: FULFILLMENT_SERVICE
    value: http://fulfillment-service.services
  - name: INVOICE_FRONT_END_URL
    value: https://business.wedeliverapp.com/sa/public/invoice/
  - name: KAFAKA_URL
    value: ''
  - name: S3_REGION
    value: eu-west-1
  - name: S3_BUCKET
    value: thrivve-customers-assets
  - name: AMAZON_SLIP_URL
    value: https://wedeliver-backend-assets.s3-eu-west-1.amazonaws.com/
  - name: INVOICE_SERVICE
    value: http://invoice-service.services
  - name: WEB_BASE_URL
    value: https://business.wedeliverapp.com
jobs:
  watch-unpaid-invoices:
    suspend: false
  watch-unapproved-invoices:
    suspend: false
  watch-performance-report:
    suspend: false
