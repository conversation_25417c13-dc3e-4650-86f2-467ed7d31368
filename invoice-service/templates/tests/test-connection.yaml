apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "invoice-service.fullname" . }}-test-connection"
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "invoice-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "invoice-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
